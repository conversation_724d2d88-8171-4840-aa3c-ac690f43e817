/**
 * ContactList component
 *
 * Displays a list of contacts with search, filtering, and pagination
 */

'use client';

import { useState, useEffect } from 'react';
import { ContactListProps, ContactSummary, ContactSearchCriteria } from '@/types/contact';
import { ContactCard } from './ContactCard';
// import { ContactFilter } from './ContactFilter'; // TODO: Create ContactFilter component
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useContacts, useDeleteContact } from '@/hooks';

// Icons
const SearchIcon = () => <span>🔍</span>;
const FilterIcon = () => <span>🔽</span>;
const AddIcon = () => <span>➕</span>;

// Remove the local state interface since we're using React Query

export function ContactList({
  transactionId,
  searchCriteria = {},
  onContactSelect,
  onContactEdit,
  onContactDelete,
  showActions = true,
  compact = false,
}: ContactListProps) {
  const [criteria, setCriteria] = useState<ContactSearchCriteria>({
    ...searchCriteria,
    transactionId,
    page: 1,
    limit: 10,
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Use React Query hooks
  const { data: contactsData, isLoading, error, refetch } = useContacts(criteria);
  const deleteContactMutation = useDeleteContact();

  // Extract data from React Query response
  const contacts = contactsData?.contacts || [];
  const total = contactsData?.total || 0;
  const totalPages = contactsData?.totalPages || 1;

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCriteria(prev => ({
      ...prev,
      search: term || undefined,
      page: 1,
    }));
  };

  const handleCriteriaChange = (newCriteria: ContactSearchCriteria) => {
    setCriteria(prev => ({
      ...prev,
      ...newCriteria,
      page: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setCriteria(prev => ({ ...prev, page }));
  };

  const handleContactSelect = (contact: ContactSummary) => {
    if (onContactSelect) {
      onContactSelect(contact as any); // Type conversion for compatibility
    }
  };

  const handleContactEdit = (contact: ContactSummary) => {
    if (onContactEdit) {
      onContactEdit(contact as any); // Type conversion for compatibility
    }
  };

  const handleContactDelete = async (contactId: string) => {
    try {
      await deleteContactMutation.mutateAsync(contactId);
      if (onContactDelete) {
        onContactDelete(contactId);
      }
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Error deleting contact:', error);
    }
  };

  const resetFilters = () => {
    setSearchTerm('');
    setCriteria({
      transactionId,
      page: 1,
      limit: 10,
    });
    setShowFilters(false);
  };

  if (isLoading && contacts.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">Loading contacts...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600">{error instanceof Error ? error.message : 'Failed to load contacts'}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          className="mt-2"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon />
            </div>
            <Input
              type="text"
              placeholder="Search contacts..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            leftIcon={<FilterIcon />}
          >
            Filters
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-sm text-gray-600">Contact filters will be implemented here</p>
          {/* TODO: Implement ContactFilter component */}
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          {total === 0 ? 'No contacts found' : `${total} contact${total === 1 ? '' : 's'} found`}
        </p>

        {isLoading && (
          <LoadingSpinner size="sm" />
        )}
      </div>

      {/* Contact Grid */}
      {contacts.length > 0 ? (
        <div className={`grid gap-4 ${compact ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 lg:grid-cols-2'}`}>
          {contacts.map((contact) => (
            <ContactCard
              key={contact.id}
              contact={contact}
              onEdit={showActions ? handleContactEdit : undefined}
              onDelete={showActions ? handleContactDelete : undefined}
              onView={handleContactSelect}
              showActions={showActions}
              compact={compact}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No contacts found</p>
          {transactionId && (
            <p className="text-sm text-gray-400">
              Add contacts to this transaction to get started
            </p>
          )}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(criteria.page! - 1)}
            disabled={criteria.page === 1 || isLoading}
          >
            Previous
          </Button>

          <span className="text-sm text-gray-600">
            Page {criteria.page} of {totalPages}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(criteria.page! + 1)}
            disabled={criteria.page === totalPages || isLoading}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}

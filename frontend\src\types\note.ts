/**
 * Note-related types for the frontend
 */

import { PaginatedResponse } from './api';

/**
 * Note interface
 */
export interface Note {
  id: string;
  transactionId: string;
  userId: string;
  content: string;
  mentions: string[];
  createdAt: string;
  updatedAt: string;
  
  // Related entities
  transaction: {
    id: string;
    propertyAddress: string;
    status: string;
  };
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  mentionedUsers?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  }[];
  
  // Calculated fields
  isEdited: boolean;
  canEdit: boolean;
  canDelete: boolean;
  timeAgo: string;
  mentionCount: number;
}

/**
 * Note summary (for lists)
 */
export interface NoteSummary {
  id: string;
  transactionId: string;
  userId: string;
  content: string;
  mentions: string[];
  createdAt: string;
  updatedAt: string;
  
  // Related entities
  transaction: {
    id: string;
    propertyAddress: string;
  };
  user: {
    id: string;
    firstName: string;
    lastName: string;
  };
  
  // Calculated fields
  isEdited: boolean;
  timeAgo: string;
  mentionCount: number;
  contentPreview: string;
}

/**
 * Note creation data
 */
export interface CreateNoteData {
  transactionId: string;
  content: string;
  mentions?: string[];
}

/**
 * Note update data
 */
export interface UpdateNoteData {
  content?: string;
  mentions?: string[];
}

/**
 * Note search criteria
 */
export interface NoteSearchCriteria {
  transactionId?: string;
  userId?: string;
  mentionedUserId?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'content';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Note list response
 */
export interface NoteListResponse extends PaginatedResponse<NoteSummary> {
  notes: NoteSummary[];
}

/**
 * Note statistics
 */
export interface NoteStats {
  total: number;
  byUser: Record<string, number>;
  byTransaction: Record<string, number>;
  totalMentions: number;
  recentActivity: number;
  averageNotesPerTransaction: number;
  mostActiveUsers: {
    userId: string;
    userName: string;
    noteCount: number;
  }[];
}

/**
 * Note mention data
 */
export interface NoteMention {
  userId: string;
  userName: string;
  userEmail: string;
  noteId: string;
  noteContent: string;
  transactionId: string;
  propertyAddress: string;
  mentionedAt: string;
  isRead: boolean;
}

/**
 * Note thread data
 */
export interface NoteThread {
  transactionId: string;
  propertyAddress: string;
  notes: NoteSummary[];
  participants: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    noteCount: number;
    lastActivity: string;
  }[];
  totalNotes: number;
  lastActivity: string;
}

/**
 * Bulk note operation
 */
export interface BulkNoteOperation {
  noteIds: string[];
  operation: 'delete' | 'export';
}

/**
 * Note form validation
 */
export interface NoteFormErrors {
  content?: string;
  mentions?: string;
  general?: string;
}

/**
 * Note modal props
 */
export interface NoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  note?: Note;
  transactionId?: string;
  mode: 'create' | 'edit' | 'view';
  onSave?: (note: Note) => void;
}

/**
 * Note list props
 */
export interface NoteListProps {
  transactionId?: string;
  searchCriteria?: NoteSearchCriteria;
  onNoteSelect?: (note: Note) => void;
  onNoteEdit?: (note: Note) => void;
  onNoteDelete?: (noteId: string) => void;
  showActions?: boolean;
  compact?: boolean;
  showThread?: boolean;
}

/**
 * Note card props
 */
export interface NoteCardProps {
  note: NoteSummary;
  onEdit?: (note: NoteSummary) => void;
  onDelete?: (noteId: string) => void;
  onView?: (note: NoteSummary) => void;
  onReply?: (note: NoteSummary) => void;
  showActions?: boolean;
  compact?: boolean;
  currentUserId?: string;
}

/**
 * Note form props
 */
export interface NoteFormProps {
  note?: Note;
  transactionId?: string;
  onSubmit: (data: CreateNoteData | UpdateNoteData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  errors?: NoteFormErrors;
  placeholder?: string;
  showMentions?: boolean;
  availableUsers?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  }[];
}

/**
 * Note thread props
 */
export interface NoteThreadProps {
  transactionId: string;
  onNoteAdd?: (note: Note) => void;
  onNoteEdit?: (note: Note) => void;
  onNoteDelete?: (noteId: string) => void;
  currentUserId?: string;
  compact?: boolean;
}

/**
 * Mention input props
 */
export interface MentionInputProps {
  value: string;
  onChange: (value: string, mentions: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  availableUsers?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  }[];
  maxLength?: number;
}

/**
 * User mention data
 */
export interface UserMention {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  displayName: string;
}

/**
 * Note validation helpers
 */
export const NoteValidation = {
  /**
   * Validate note content
   */
  isValidContent: (content: string): boolean => {
    return content.trim().length > 0 && content.length <= 2000;
  },

  /**
   * Extract mentions from content
   */
  extractMentions: (content: string): string[] => {
    const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
    const mentions: string[] = [];
    let match;
    
    while ((match = mentionRegex.exec(content)) !== null) {
      const userId = match[2];
      if (userId && !mentions.includes(userId)) {
        mentions.push(userId);
      }
    }
    
    return mentions;
  },

  /**
   * Format content with mentions for display
   */
  formatContentWithMentions: (content: string, users: UserMention[]): string => {
    let formattedContent = content;
    
    users.forEach(user => {
      const mentionPattern = new RegExp(`@\\[([^\\]]+)\\]\\(${user.id}\\)`, 'g');
      formattedContent = formattedContent.replace(
        mentionPattern,
        `@${user.firstName} ${user.lastName}`
      );
    });
    
    return formattedContent;
  },

  /**
   * Create mention markup for content
   */
  createMentionMarkup: (userId: string, userName: string): string => {
    return `@[${userName}](${userId})`;
  },

  /**
   * Get content preview (truncated)
   */
  getContentPreview: (content: string, maxLength = 100): string => {
    if (content.length <= maxLength) {
      return content;
    }
    return content.substring(0, maxLength).trim() + '...';
  },

  /**
   * Calculate time ago string
   */
  getTimeAgo: (date: string): string => {
    const now = new Date();
    const noteDate = new Date(date);
    const diffInSeconds = Math.floor((now.getTime() - noteDate.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return noteDate.toLocaleDateString();
    }
  },

  /**
   * Check if note was edited
   */
  isEdited: (createdAt: string, updatedAt: string): boolean => {
    return new Date(updatedAt).getTime() - new Date(createdAt).getTime() > 1000;
  },
};

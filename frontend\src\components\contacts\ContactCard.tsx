/**
 * ContactCard component
 * 
 * Displays a contact in a card format with actions
 */

'use client';

import { useState } from 'react';
import { ContactCardProps, ContactRoleDisplayNames } from '@/types/contact';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';

// Icons (using simple text for now, can be replaced with icon library)
const EditIcon = () => <span>✏️</span>;
const DeleteIcon = () => <span>🗑️</span>;
const ViewIcon = () => <span>👁️</span>;
const EmailIcon = () => <span>📧</span>;
const PhoneIcon = () => <span>📞</span>;
const CompanyIcon = () => <span>🏢</span>;

export function ContactCard({
  contact,
  onEdit,
  onDelete,
  onView,
  showActions = true,
  compact = false,
}: ContactCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!onDelete) return;
    
    if (window.confirm(`Are you sure you want to delete ${contact.fullName}?`)) {
      setIsDeleting(true);
      try {
        await onDelete(contact.id);
      } catch (error) {
        console.error('Error deleting contact:', error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(contact);
    }
  };

  const handleView = () => {
    if (onView) {
      onView(contact);
    }
  };

  const formatPhone = (phone: string) => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  };

  return (
    <div
      className={cn(
        'bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow',
        compact ? 'p-3' : 'p-4',
        'group'
      )}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <h3 className={cn(
            'font-semibold text-gray-900 truncate',
            compact ? 'text-sm' : 'text-base'
          )}>
            {contact.fullName}
          </h3>
          <div className="flex items-center mt-1">
            <span className={cn(
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
              'bg-blue-100 text-blue-800'
            )}>
              {ContactRoleDisplayNames[contact.role]}
            </span>
          </div>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {onView && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleView}
                className="p-1 h-8 w-8"
                title="View contact"
              >
                <ViewIcon />
              </Button>
            )}
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="p-1 h-8 w-8"
                title="Edit contact"
              >
                <EditIcon />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                loading={isDeleting}
                className="p-1 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                title="Delete contact"
              >
                <DeleteIcon />
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Contact Information */}
      <div className="space-y-2">
        {contact.email && (
          <div className="flex items-center text-sm text-gray-600">
            <EmailIcon />
            <span className="ml-2 truncate">
              <a
                href={`mailto:${contact.email}`}
                className="text-blue-600 hover:text-blue-700 hover:underline"
              >
                {contact.email}
              </a>
            </span>
          </div>
        )}

        {contact.phone && (
          <div className="flex items-center text-sm text-gray-600">
            <PhoneIcon />
            <span className="ml-2">
              <a
                href={`tel:${contact.phone}`}
                className="text-blue-600 hover:text-blue-700 hover:underline"
              >
                {formatPhone(contact.phone)}
              </a>
            </span>
          </div>
        )}

        {contact.company && (
          <div className="flex items-center text-sm text-gray-600">
            <CompanyIcon />
            <span className="ml-2 truncate" title={contact.company}>
              {contact.company}
            </span>
          </div>
        )}
      </div>

      {/* Transaction Info */}
      {!compact && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="text-xs text-gray-500">
            <span className="font-medium">Transaction:</span>
            <span className="ml-1 truncate" title={contact.transaction.propertyAddress}>
              {contact.transaction.propertyAddress}
            </span>
          </div>
          <div className="text-xs text-gray-400 mt-1">
            Added {new Date(contact.createdAt).toLocaleDateString()}
          </div>
        </div>
      )}
    </div>
  );
}

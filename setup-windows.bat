@echo off
setlocal enabledelayedexpansion

:: TC Platform Windows Setup Script (Batch Version)
:: This script provides a simpler alternative to the PowerShell script

title TC Platform Setup

echo.
echo ========================================
echo    TC Platform Windows Setup Script
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

:: Check if we're in the right directory
if not exist "backend" (
    echo ERROR: backend folder not found.
    echo Please run this script from the TC Platform root directory.
    echo.
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ERROR: frontend folder not found.
    echo Please run this script from the TC Platform root directory.
    echo.
    pause
    exit /b 1
)

echo Step 1: Checking prerequisites...
echo.

:: Check for Node.js
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo Node.js not found. Please install Node.js 18+ from https://nodejs.org/
    echo After installation, restart this script.
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Node.js found
)

:: Check for npm
npm --version >nul 2>&1
if %errorLevel% neq 0 (
    echo npm not found. Please ensure Node.js is properly installed.
    echo.
    pause
    exit /b 1
) else (
    echo ✓ npm found
)

:: Check for PostgreSQL
pg_isready --version >nul 2>&1
if %errorLevel% neq 0 (
    echo PostgreSQL not found. Please install PostgreSQL 14+ from https://www.postgresql.org/download/windows/
    echo Make sure to add PostgreSQL to your PATH during installation.
    echo After installation, restart this script.
    echo.
    pause
    exit /b 1
) else (
    echo ✓ PostgreSQL found
)

echo.
echo Step 2: Setting up database...
echo.

:: Set database password
set DB_PASSWORD=tc_password_2024

:: Start PostgreSQL service (if not running)
net start postgresql-x64-15 >nul 2>&1

:: Wait a moment for service to start
timeout /t 3 /nobreak >nul

:: Create database and user
echo Creating database and user...
(
echo CREATE DATABASE tc_platform;
echo CREATE USER tc_user WITH PASSWORD '%DB_PASSWORD%';
echo GRANT ALL PRIVILEGES ON DATABASE tc_platform TO tc_user;
echo ALTER USER tc_user CREATEDB;
) | psql -U postgres -h localhost

if %errorLevel% neq 0 (
    echo ERROR: Failed to create database. Please ensure PostgreSQL is running and you have admin access.
    echo You may need to set a password for the postgres user first.
    echo.
    pause
    exit /b 1
)

echo ✓ Database created successfully

echo.
echo Step 3: Installing dependencies...
echo.

:: Install backend dependencies
echo Installing backend dependencies...
cd backend
call npm install
if %errorLevel% neq 0 (
    echo ERROR: Failed to install backend dependencies
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✓ Backend dependencies installed

:: Install frontend dependencies
echo Installing frontend dependencies...
cd frontend
call npm install
if %errorLevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✓ Frontend dependencies installed

echo.
echo Step 4: Creating environment files...
echo.

:: Generate JWT secret
set JWT_SECRET=tc_jwt_secret_change_in_production_%RANDOM%%RANDOM%

:: Create backend .env file
(
echo # Database
echo DATABASE_URL="postgresql://tc_user:%DB_PASSWORD%@localhost:5432/tc_platform"
echo.
echo # JWT
echo JWT_SECRET="%JWT_SECRET%"
echo JWT_EXPIRES_IN="7d"
echo.
echo # Server
echo PORT=3001
echo NODE_ENV=development
echo.
echo # CORS
echo FRONTEND_URL="http://localhost:3000"
) > backend\.env

echo ✓ Backend .env file created

:: Create frontend .env.local file
(
echo # API Configuration
echo NEXT_PUBLIC_API_URL=http://localhost:3001
echo NEXT_PUBLIC_APP_URL=http://localhost:3000
echo.
echo # Environment
echo NODE_ENV=development
) > frontend\.env.local

echo ✓ Frontend .env.local file created

echo.
echo Step 5: Setting up database schema...
echo.

cd backend

:: Generate Prisma client
echo Generating Prisma client...
call npx prisma generate
if %errorLevel% neq 0 (
    echo ERROR: Failed to generate Prisma client
    cd ..
    pause
    exit /b 1
)
echo ✓ Prisma client generated

:: Run migrations
echo Running database migrations...
call npx prisma migrate dev --name init
if %errorLevel% neq 0 (
    echo ERROR: Failed to run database migrations
    cd ..
    pause
    exit /b 1
)
echo ✓ Database migrations completed

:: Seed database
echo Seeding database with sample data...
call npx prisma db seed
if %errorLevel% neq 0 (
    echo WARNING: Failed to seed database (this is optional)
) else (
    echo ✓ Database seeded with sample data
)

cd ..

echo.
echo Step 6: Creating startup scripts...
echo.

:: Create start-backend.bat
(
echo @echo off
echo echo Starting TC Platform Backend...
echo cd backend
echo npm run dev
echo pause
) > start-backend.bat
echo ✓ start-backend.bat created

:: Create start-frontend.bat
(
echo @echo off
echo echo Starting TC Platform Frontend...
echo cd frontend
echo npm run dev
echo pause
) > start-frontend.bat
echo ✓ start-frontend.bat created

:: Create start-both.bat
(
echo @echo off
echo echo Starting TC Platform ^(Backend and Frontend^)...
echo echo.
echo echo Starting Backend...
echo start "TC Platform Backend" cmd /k "cd backend && npm run dev"
echo timeout /t 5 /nobreak ^> nul
echo echo Starting Frontend...
echo start "TC Platform Frontend" cmd /k "cd frontend && npm run dev"
echo echo.
echo echo Both services are starting...
echo echo Backend: http://localhost:3001
echo echo Frontend: http://localhost:3000
echo echo.
echo pause
) > start-both.bat
echo ✓ start-both.bat created

echo.
echo ========================================
echo    🎉 Setup Complete! 🎉
echo ========================================
echo.
echo What was installed:
echo   ✓ Project dependencies
echo   ✓ Database schema and sample data
echo   ✓ Environment configuration
echo   ✓ Startup scripts
echo.
echo How to start the application:
echo   Option 1: Double-click 'start-both.bat' (recommended)
echo   Option 2: Run 'start-backend.bat' and 'start-frontend.bat' separately
echo.
echo Access points:
echo   • Main App: http://localhost:3000
echo   • Dashboard: http://localhost:3000/dashboard
echo   • Contact Demo: http://localhost:3000/demo/contact-communication
echo   • Backend API: http://localhost:3001
echo.
echo Database info:
echo   • Database: tc_platform
echo   • User: tc_user
echo   • Password: %DB_PASSWORD%
echo   • Host: localhost:5432
echo.
echo Next steps:
echo   1. Double-click 'start-both.bat' to start the application
echo   2. Open http://localhost:3000 in your browser
echo   3. Explore the Contact ^& Communication features
echo.
echo Setup completed successfully! 🎉
echo.
pause

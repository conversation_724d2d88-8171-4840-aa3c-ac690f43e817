/**
 * Demo page for Contact & Communication Management
 * 
 * This page demonstrates the integrated contact and note components
 * with real API integration using React Query.
 */

'use client';

import { useState } from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { ContactManagement } from '@/components/contacts';
import { NoteManagement } from '@/components/notes';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function DemoPageContent() {
  const [activeTab, setActiveTab] = useState<'contacts' | 'notes'>('contacts');
  const [selectedTransactionId] = useState('demo-transaction-123');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">
              Contact & Communication Management Demo
            </h1>
            <p className="mt-2 text-gray-600">
              Integrated contact management and real-time communication system
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('contacts')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'contacts'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              👥 Contact Management
            </button>
            <button
              onClick={() => setActiveTab('notes')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'notes'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              💬 Notes & Communication
            </button>
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'contacts' && (
          <div className="space-y-6">
            {/* Demo Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                Contact Management Features
              </h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Create, edit, and delete contacts with role-based permissions</li>
                <li>• Search and filter contacts by role, company, or name</li>
                <li>• Real-time updates using React Query</li>
                <li>• Responsive design with mobile-friendly interface</li>
                <li>• Bulk operations and export functionality</li>
              </ul>
            </div>

            {/* Contact Management Component */}
            <ContactManagement
              transactionId={selectedTransactionId}
              title="Transaction Contacts"
              showAddButton={true}
              compact={false}
            />
          </div>
        )}

        {activeTab === 'notes' && (
          <div className="space-y-6">
            {/* Demo Info */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-green-900 mb-2">
                Communication Features
              </h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Real-time note creation with @mention support</li>
                <li>• Thread view for conversation-style communication</li>
                <li>• User notifications for mentions and replies</li>
                <li>• Rich text formatting and content preview</li>
                <li>• Search and filter notes by content or user</li>
              </ul>
            </div>

            {/* Note Management Component */}
            <NoteManagement
              transactionId={selectedTransactionId}
              title="Transaction Communication"
              showAddButton={true}
              compact={false}
              defaultView="list"
              currentUserId="user-1"
            />
          </div>
        )}
      </div>

      {/* API Status */}
      <div className="fixed bottom-4 right-4">
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">Demo Mode</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Using mock data. Connect to backend API for full functionality.
          </p>
        </div>
      </div>
    </div>
  );
}

export default function DemoPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <DemoPageContent />
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
    </QueryClientProvider>
  );
}

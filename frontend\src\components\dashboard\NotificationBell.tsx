/**
 * NotificationBell component
 * 
 * Notification bell with unread count for mentions and messages
 */

'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useUnreadMentionsCount, useUserMentions } from '@/hooks';
import { NoteValidation } from '@/types/note';

interface NotificationBellProps {
  currentUserId?: string;
}

export function NotificationBell({ currentUserId = 'user-1' }: NotificationBellProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // React Query hooks
  const { data: unreadCount = 0 } = useUnreadMentionsCount(currentUserId);
  const { data: mentions = [] } = useUserMentions(currentUserId, true); // unread only

  // Mock mentions for demo
  const mockMentions = [
    {
      id: '1',
      noteId: 'note-1',
      noteContent: 'Can you review the inspection report for 123 Main St? @[Current User](user-1)',
      transactionId: 'trans-001',
      propertyAddress: '123 Main St, Philadelphia, PA',
      mentionedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      isRead: false,
      user: {
        firstName: 'Sarah',
        lastName: 'Wilson',
      },
    },
    {
      id: '2',
      noteId: 'note-2',
      noteContent: 'Meeting scheduled for tomorrow at 2 PM. @[Current User](user-1) please confirm.',
      transactionId: 'trans-002',
      propertyAddress: '456 Oak Ave, Philadelphia, PA',
      mentionedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      isRead: false,
      user: {
        firstName: 'Mike',
        lastName: 'Johnson',
      },
    },
    {
      id: '3',
      noteId: 'note-3',
      noteContent: 'Documents are ready for review. @[Current User](user-1) when can we schedule signing?',
      transactionId: 'trans-003',
      propertyAddress: '789 Pine St, Philadelphia, PA',
      mentionedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      isRead: false,
      user: {
        firstName: 'Lisa',
        lastName: 'Rodriguez',
      },
    },
  ];

  const displayMentions = mentions.length > 0 ? mentions : mockMentions;
  const displayCount = unreadCount > 0 ? unreadCount : mockMentions.length;

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const formatContent = (content: string) => {
    // Remove mention markup and truncate
    const cleanContent = content.replace(/@\[([^\]]+)\]\(([^)]+)\)/g, '@$1');
    return NoteValidation.getContentPreview(cleanContent, 60);
  };

  const formatTimeAgo = (dateString: string) => {
    return NoteValidation.getTimeAgo(dateString);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full"
      >
        <span className="sr-only">View notifications</span>
        <svg
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"
          />
        </svg>
        
        {/* Notification Badge */}
        {displayCount > 0 && (
          <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full animate-pulse">
            {displayCount > 9 ? '9+' : displayCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Notifications</h3>
              {displayCount > 0 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  {displayCount} new
                </span>
              )}
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {displayMentions.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {displayMentions.slice(0, 5).map((mention) => (
                  <Link
                    key={mention.id}
                    href={`/dashboard/transactions/${mention.transactionId}?tab=communication&note=${mention.noteId}`}
                    onClick={() => setIsOpen(false)}
                    className="block px-4 py-3 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start space-x-3">
                      {/* Avatar */}
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-medium">
                            {mention.user.firstName.charAt(0)}{mention.user.lastName.charAt(0)}
                          </span>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <p className="text-sm font-medium text-gray-900">
                            {mention.user.firstName} {mention.user.lastName}
                          </p>
                          <span className="text-xs text-gray-500">
                            {formatTimeAgo(mention.mentionedAt)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700 mb-1">
                          {formatContent(mention.noteContent)}
                        </p>
                        <p className="text-xs text-blue-600">
                          {mention.propertyAddress}
                        </p>
                      </div>

                      {/* Unread indicator */}
                      {!mention.isRead && (
                        <div className="flex-shrink-0">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="px-4 py-8 text-center">
                <div className="text-gray-400 mb-2">
                  <svg className="mx-auto h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7H6a2 2 0 00-2 2v9a2 2 0 002 2h8a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 0V4a2 2 0 00-2-2H9a2 2 0 00-2 2v3m1 0h2" />
                  </svg>
                </div>
                <p className="text-sm text-gray-500">No new notifications</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <Link
                href="/demo/contact-communication"
                onClick={() => setIsOpen(false)}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                View all messages
              </Link>
              <button
                onClick={() => setIsOpen(false)}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Mark all as read
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Note API client
 */

import { apiClient } from '../api';
import {
  Note,
  NoteSummary,
  CreateNoteData,
  UpdateNoteData,
  NoteSearchCriteria,
  NoteListResponse,
  NoteStats,
  NoteMention,
  NoteThread,
  BulkNoteOperation,
} from '@/types/note';
import { ApiResponse, PaginatedResponse } from '@/types/api';

/**
 * Note API endpoints
 */
export const noteApi = {
  /**
   * Get all notes with search and pagination
   */
  getNotes: async (criteria: NoteSearchCriteria = {}): Promise<NoteListResponse> => {
    const params = new URLSearchParams();
    
    if (criteria.transactionId) params.append('transactionId', criteria.transactionId);
    if (criteria.userId) params.append('userId', criteria.userId);
    if (criteria.mentionedUserId) params.append('mentionedUserId', criteria.mentionedUserId);
    if (criteria.search) params.append('search', criteria.search);
    if (criteria.dateFrom) params.append('dateFrom', criteria.dateFrom);
    if (criteria.dateTo) params.append('dateTo', criteria.dateTo);
    if (criteria.sortBy) params.append('sortBy', criteria.sortBy);
    if (criteria.sortOrder) params.append('sortOrder', criteria.sortOrder);
    if (criteria.page) params.append('page', criteria.page.toString());
    if (criteria.limit) params.append('limit', criteria.limit.toString());

    const response = await apiClient.get<PaginatedResponse<NoteSummary>>(
      `/notes?${params.toString()}`
    );

    return {
      notes: response.data.items,
      items: response.data.items,
      total: response.data.total,
      page: response.data.page,
      limit: response.data.limit,
      totalPages: response.data.totalPages,
    };
  },

  /**
   * Get note by ID
   */
  getNote: async (id: string): Promise<Note> => {
    const response = await apiClient.get<Note>(`/notes/${id}`);
    return response.data;
  },

  /**
   * Create new note
   */
  createNote: async (data: CreateNoteData): Promise<Note> => {
    const response = await apiClient.post<Note>('/notes', data);
    return response.data;
  },

  /**
   * Update note
   */
  updateNote: async (id: string, data: UpdateNoteData): Promise<Note> => {
    const response = await apiClient.put<Note>(`/notes/${id}`, data);
    return response.data;
  },

  /**
   * Delete note
   */
  deleteNote: async (id: string): Promise<void> => {
    await apiClient.delete(`/notes/${id}`);
  },

  /**
   * Get note statistics
   */
  getNoteStats: async (transactionId?: string): Promise<NoteStats> => {
    const params = transactionId ? `?transactionId=${transactionId}` : '';
    const response = await apiClient.get<NoteStats>(`/notes/stats${params}`);
    return response.data;
  },

  /**
   * Get notes by transaction ID
   */
  getNotesByTransaction: async (transactionId: string): Promise<NoteSummary[]> => {
    const response = await apiClient.get<NoteSummary[]>(`/notes/transaction/${transactionId}`);
    return response.data;
  },

  /**
   * Get note thread for transaction
   */
  getNoteThread: async (transactionId: string): Promise<NoteThread> => {
    const response = await apiClient.get<NoteThread>(`/notes/transaction/${transactionId}/thread`);
    return response.data;
  },

  /**
   * Get user mentions
   */
  getUserMentions: async (userId: string, unreadOnly = false): Promise<NoteMention[]> => {
    const params = unreadOnly ? '?unreadOnly=true' : '';
    const response = await apiClient.get<NoteMention[]>(`/notes/mentions/${userId}${params}`);
    return response.data;
  },

  /**
   * Mark mention as read
   */
  markMentionAsRead: async (noteId: string, userId: string): Promise<void> => {
    await apiClient.patch(`/notes/${noteId}/mentions/${userId}/read`);
  },

  /**
   * Perform bulk operations on notes
   */
  bulkOperation: async (operation: BulkNoteOperation): Promise<void> => {
    await apiClient.post('/notes/bulk', operation);
  },

  /**
   * Export notes
   */
  exportNotes: async (criteria: NoteSearchCriteria = {}): Promise<Blob> => {
    const params = new URLSearchParams();
    
    if (criteria.transactionId) params.append('transactionId', criteria.transactionId);
    if (criteria.userId) params.append('userId', criteria.userId);
    if (criteria.search) params.append('search', criteria.search);
    if (criteria.dateFrom) params.append('dateFrom', criteria.dateFrom);
    if (criteria.dateTo) params.append('dateTo', criteria.dateTo);

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/notes/export?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error('Failed to export notes');
    }

    return response.blob();
  },

  /**
   * Search notes with autocomplete
   */
  searchNotes: async (query: string, transactionId?: string): Promise<NoteSummary[]> => {
    const params = new URLSearchParams();
    params.append('search', query);
    params.append('limit', '10');
    if (transactionId) params.append('transactionId', transactionId);

    const response = await apiClient.get<PaginatedResponse<NoteSummary>>(
      `/notes?${params.toString()}`
    );
    
    return response.data.items;
  },

  /**
   * Get users available for mentions in a transaction
   */
  getMentionableUsers: async (transactionId: string): Promise<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  }[]> => {
    const response = await apiClient.get<{
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    }[]>(`/notes/transaction/${transactionId}/mentionable-users`);
    
    return response.data;
  },

  /**
   * Get recent activity (notes) for dashboard
   */
  getRecentActivity: async (limit = 10): Promise<NoteSummary[]> => {
    const response = await apiClient.get<NoteSummary[]>(`/notes/recent?limit=${limit}`);
    return response.data;
  },

  /**
   * Get unread mentions count
   */
  getUnreadMentionsCount: async (userId: string): Promise<number> => {
    const response = await apiClient.get<{ count: number }>(`/notes/mentions/${userId}/unread-count`);
    return response.data.count;
  },
};

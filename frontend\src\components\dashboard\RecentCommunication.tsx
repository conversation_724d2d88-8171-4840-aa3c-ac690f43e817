/**
 * RecentCommunication component
 * 
 * Dashboard widget showing recent notes and mentions
 */

'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRecentActivity, useUnreadMentionsCount } from '@/hooks';
import { NoteValidation } from '@/types/note';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/Button';

interface RecentCommunicationProps {
  currentUserId?: string;
  limit?: number;
}

export function RecentCommunication({ 
  currentUserId = 'user-1', 
  limit = 5 
}: RecentCommunicationProps) {
  const [activeTab, setActiveTab] = useState<'recent' | 'mentions'>('recent');
  
  // Use React Query hooks for real-time data
  const { data: recentNotes = [], isLoading: loadingRecent } = useRecentActivity(limit);
  const { data: unreadCount = 0 } = useUnreadMentionsCount(currentUserId);

  // <PERSON>ck mentions data for demo
  const mockMentions = [
    {
      id: '1',
      noteId: 'note-1',
      noteContent: 'Can you review the inspection report for 123 Main St? @[Current User](user-1)',
      transactionId: 'trans-001',
      propertyAddress: '123 Main St, Philadelphia, PA',
      mentionedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      isRead: false,
      user: {
        firstName: 'Sarah',
        lastName: 'Wilson',
      },
    },
    {
      id: '2',
      noteId: 'note-2',
      noteContent: 'Meeting scheduled for tomorrow at 2 PM. @[Current User](user-1) please confirm.',
      transactionId: 'trans-002',
      propertyAddress: '456 Oak Ave, Philadelphia, PA',
      mentionedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      isRead: false,
      user: {
        firstName: 'Mike',
        lastName: 'Johnson',
      },
    },
  ];

  const formatContent = (content: string, maxLength = 80) => {
    // Remove mention markup for display
    const cleanContent = content.replace(/@\[([^\]]+)\]\(([^)]+)\)/g, '@$1');
    return NoteValidation.getContentPreview(cleanContent, maxLength);
  };

  const formatTimeAgo = (dateString: string) => {
    return NoteValidation.getTimeAgo(dateString);
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        {/* Header with tabs */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Communication
          </h3>
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('recent')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'recent'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Recent
            </button>
            <button
              onClick={() => setActiveTab('mentions')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors relative ${
                activeTab === 'mentions'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Mentions
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                  {unreadCount}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-3">
          {activeTab === 'recent' && (
            <>
              {loadingRecent ? (
                <div className="flex items-center justify-center py-4">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2 text-sm text-gray-600">Loading...</span>
                </div>
              ) : recentNotes.length > 0 ? (
                recentNotes.map((note) => (
                  <div key={note.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    {/* Avatar */}
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-medium">
                          {note.user.firstName.charAt(0)}{note.user.lastName.charAt(0)}
                        </span>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <p className="text-sm font-medium text-gray-900">
                          {note.user.firstName} {note.user.lastName}
                        </p>
                        <span className="text-xs text-gray-500">
                          {formatTimeAgo(note.createdAt)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 mb-1">
                        {formatContent(note.content)}
                      </p>
                      <Link
                        href={`/dashboard/transactions/${note.transactionId}?tab=communication`}
                        className="text-xs text-blue-600 hover:text-blue-700"
                      >
                        {note.transaction.propertyAddress}
                      </Link>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500">No recent activity</p>
                </div>
              )}
            </>
          )}

          {activeTab === 'mentions' && (
            <>
              {mockMentions.length > 0 ? (
                mockMentions.map((mention) => (
                  <div key={mention.id} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                    {/* Avatar */}
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-medium">
                          {mention.user.firstName.charAt(0)}{mention.user.lastName.charAt(0)}
                        </span>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <p className="text-sm font-medium text-gray-900">
                          {mention.user.firstName} {mention.user.lastName}
                        </p>
                        <span className="text-xs text-gray-500">
                          {formatTimeAgo(mention.mentionedAt)}
                        </span>
                        {!mention.isRead && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            New
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-700 mb-1">
                        {formatContent(mention.noteContent)}
                      </p>
                      <Link
                        href={`/dashboard/transactions/${mention.transactionId}?tab=communication&note=${mention.noteId}`}
                        className="text-xs text-blue-600 hover:text-blue-700"
                      >
                        {mention.propertyAddress}
                      </Link>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500">No mentions</p>
                </div>
              )}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <Link
              href="/demo/contact-communication"
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              View Communication Demo →
            </Link>
            <Button variant="outline" size="sm">
              💬 Start Conversation
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

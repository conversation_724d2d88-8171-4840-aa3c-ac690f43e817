# TC Platform Setup & Run Guide

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18+ installed
- **PostgreSQL** 14+ installed and running
- **Git** installed
- **npm** or **yarn** package manager

### 1. <PERSON>lone and Setup

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd tc-platform

# Install dependencies for both backend and frontend
cd backend && npm install
cd ../frontend && npm install
```

### 2. Database Setup

```bash
# Start PostgreSQL service
# On macOS with Homebrew:
brew services start postgresql

# On Ubuntu/Debian:
sudo systemctl start postgresql

# On Windows:
# Start PostgreSQL from Services or pgAdmin
```

Create database and user:
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database and user
CREATE DATABASE tc_platform;
CREATE USER tc_user WITH PASSWORD 'tc_password';
GRANT ALL PRIVILEGES ON DATABASE tc_platform TO tc_user;
\q
```

### 3. Environment Configuration

Create `.env` files:

**Backend (.env):**
```bash
cd backend
cat > .env << EOF
# Database
DATABASE_URL="postgresql://tc_user:tc_password@localhost:5432/tc_platform"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV=development

# CORS
FRONTEND_URL="http://localhost:3000"
EOF
```

**Frontend (.env.local):**
```bash
cd frontend
cat > .env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Environment
NODE_ENV=development
EOF
```

### 4. Database Migration

```bash
cd backend

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev --name init

# Seed database with sample data (optional)
npx prisma db seed
```

### 5. Start the Application

**Option A: Run Both Services Separately**

Terminal 1 (Backend):
```bash
cd backend
npm run dev
# Backend will start on http://localhost:3001
```

Terminal 2 (Frontend):
```bash
cd frontend
npm run dev
# Frontend will start on http://localhost:3000
```

**Option B: Run Both Services Together (if configured)**
```bash
# From root directory
npm run dev
```

### 6. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api-docs (if configured)

## 🎯 Testing the Contact & Communication Features

### 1. Access the Demo
Navigate to: http://localhost:3000/demo/contact-communication

### 2. Test Contact Management
- Click "Add Contact" to create new contacts
- Use search and filter functionality
- Edit and delete contacts
- View contact statistics

### 3. Test Communication Features
- Create notes with @mentions
- Reply to existing notes
- View real-time updates
- Check notification system

### 4. Test Dashboard Integration
Navigate to: http://localhost:3000/dashboard
- View recent communication widget
- Use quick communication panel
- Check notification bell
- View communication statistics

### 5. Test Transaction Integration
Navigate to: http://localhost:3000/dashboard/transactions
- Click on a transaction
- Test Contacts tab
- Test Communication tab
- Verify context-aware features

## 🔧 Development Commands

### Backend Commands
```bash
cd backend

# Start development server
npm run dev

# Run tests
npm test

# Run database migrations
npx prisma migrate dev

# Reset database
npx prisma migrate reset

# View database
npx prisma studio

# Generate Prisma client
npx prisma generate

# Format code
npm run format

# Lint code
npm run lint
```

### Frontend Commands
```bash
cd frontend

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run tests
npm test

# Run E2E tests
npm run test:e2e

# Type checking
npm run type-check

# Lint code
npm run lint

# Format code
npm run format
```

## 🐛 Troubleshooting

### Common Issues

**1. Database Connection Error**
```bash
# Check if PostgreSQL is running
pg_isready -h localhost -p 5432

# Check database exists
psql -U tc_user -d tc_platform -c "\dt"
```

**2. Port Already in Use**
```bash
# Kill process on port 3000
lsof -ti:3000 | xargs kill -9

# Kill process on port 3001
lsof -ti:3001 | xargs kill -9
```

**3. Prisma Client Issues**
```bash
cd backend
npx prisma generate
npm run dev
```

**4. Node Modules Issues**
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install
```

**5. Environment Variables Not Loading**
```bash
# Check .env files exist
ls -la backend/.env
ls -la frontend/.env.local

# Restart development servers
```

### Database Issues

**Reset Database:**
```bash
cd backend
npx prisma migrate reset
npx prisma migrate dev
npx prisma db seed
```

**View Database:**
```bash
cd backend
npx prisma studio
# Opens database viewer at http://localhost:5555
```

## 📊 Sample Data

The system includes sample data for testing:

### Sample Users
- **TC User**: <EMAIL>
- **Agent 1**: <EMAIL>  
- **Agent 2**: <EMAIL>

### Sample Transactions
- **123 Main St, Philadelphia, PA** - Active purchase
- **456 Oak Ave, Philadelphia, PA** - Under contract sale
- **789 Pine St, Philadelphia, PA** - Pending purchase

### Sample Contacts
- **John & Jane Smith** - Buyers
- **Robert Johnson** - Seller
- **Michael Chen** - Listing Agent

## 🔍 API Testing

### Using curl
```bash
# Get all contacts
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:3001/api/contacts

# Create a contact
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -d '{"firstName":"Test","lastName":"User","email":"<EMAIL>","role":"buyer","transactionId":"TRANSACTION_ID"}' \
     http://localhost:3001/api/contacts

# Get all notes
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:3001/api/notes

# Create a note
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -d '{"content":"Test note","transactionId":"TRANSACTION_ID"}' \
     http://localhost:3001/api/notes
```

### Using Postman
Import the API collection (if available) or manually test endpoints:
- Base URL: http://localhost:3001
- Add Authorization header: `Bearer YOUR_JWT_TOKEN`
- Test all 20 Contact & Communication endpoints

## 🚀 Production Deployment

### Build for Production
```bash
# Backend
cd backend
npm run build

# Frontend
cd frontend
npm run build
```

### Environment Variables for Production
Update `.env` files with production values:
- Database connection string
- JWT secret (use strong random key)
- CORS origins
- API URLs

### Docker Deployment (if configured)
```bash
# Build and run with Docker Compose
docker-compose up --build

# Or run individual services
docker build -t tc-backend ./backend
docker build -t tc-frontend ./frontend
```

## 📱 Mobile Testing

### Local Network Testing
```bash
# Find your local IP
ipconfig getifaddr en0  # macOS
ip route get 1 | awk '{print $7}'  # Linux

# Update frontend .env.local
NEXT_PUBLIC_API_URL=http://YOUR_LOCAL_IP:3001

# Access from mobile device
http://YOUR_LOCAL_IP:3000
```

## 🎯 Next Steps

1. **Explore the Features**: Test all contact and communication functionality
2. **Customize**: Modify components and styling as needed
3. **Integrate**: Connect with your existing systems
4. **Deploy**: Follow the production deployment guide
5. **Monitor**: Set up monitoring and analytics
6. **Scale**: Optimize for your user base

The TC Platform with Contact & Communication Management is now ready to use! 🎉

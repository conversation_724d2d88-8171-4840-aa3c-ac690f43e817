/**
 * Contact API client
 */

import { apiClient } from '../api';
import {
  Contact,
  ContactSummary,
  CreateContactData,
  UpdateContactData,
  ContactSearchCriteria,
  ContactListResponse,
  ContactStats,
  BulkContactOperation,
} from '@/types/contact';
import { ApiResponse, PaginatedResponse } from '@/types/api';

/**
 * Contact API endpoints
 */
export const contactApi = {
  /**
   * Get all contacts with search and pagination
   */
  getContacts: async (criteria: ContactSearchCriteria = {}): Promise<ContactListResponse> => {
    const params = new URLSearchParams();
    
    if (criteria.transactionId) params.append('transactionId', criteria.transactionId);
    if (criteria.role && criteria.role.length > 0) {
      criteria.role.forEach(role => params.append('role', role));
    }
    if (criteria.company) params.append('company', criteria.company);
    if (criteria.search) params.append('search', criteria.search);
    if (criteria.sortBy) params.append('sortBy', criteria.sortBy);
    if (criteria.sortOrder) params.append('sortOrder', criteria.sortOrder);
    if (criteria.page) params.append('page', criteria.page.toString());
    if (criteria.limit) params.append('limit', criteria.limit.toString());

    const response = await apiClient.get<PaginatedResponse<ContactSummary>>(
      `/contacts?${params.toString()}`
    );

    return {
      contacts: response.data.items,
      items: response.data.items,
      total: response.data.total,
      page: response.data.page,
      limit: response.data.limit,
      totalPages: response.data.totalPages,
    };
  },

  /**
   * Get contact by ID
   */
  getContact: async (id: string): Promise<Contact> => {
    const response = await apiClient.get<Contact>(`/contacts/${id}`);
    return response.data;
  },

  /**
   * Create new contact
   */
  createContact: async (data: CreateContactData): Promise<Contact> => {
    const response = await apiClient.post<Contact>('/contacts', data);
    return response.data;
  },

  /**
   * Update contact
   */
  updateContact: async (id: string, data: UpdateContactData): Promise<Contact> => {
    const response = await apiClient.put<Contact>(`/contacts/${id}`, data);
    return response.data;
  },

  /**
   * Delete contact
   */
  deleteContact: async (id: string): Promise<void> => {
    await apiClient.delete(`/contacts/${id}`);
  },

  /**
   * Get contact statistics
   */
  getContactStats: async (transactionId?: string): Promise<ContactStats> => {
    const params = transactionId ? `?transactionId=${transactionId}` : '';
    const response = await apiClient.get<ContactStats>(`/contacts/stats${params}`);
    return response.data;
  },

  /**
   * Get available contact roles
   */
  getContactRoles: async (): Promise<string[]> => {
    const response = await apiClient.get<string[]>('/contacts/roles');
    return response.data;
  },

  /**
   * Get contacts by transaction ID
   */
  getContactsByTransaction: async (transactionId: string): Promise<ContactSummary[]> => {
    const response = await apiClient.get<ContactSummary[]>(`/contacts/transaction/${transactionId}`);
    return response.data;
  },

  /**
   * Get contacts by role
   */
  getContactsByRole: async (role: string, transactionId?: string): Promise<ContactSummary[]> => {
    const params = transactionId ? `?transactionId=${transactionId}` : '';
    const response = await apiClient.get<ContactSummary[]>(`/contacts/role/${role}${params}`);
    return response.data;
  },

  /**
   * Perform bulk operations on contacts
   */
  bulkOperation: async (operation: BulkContactOperation): Promise<void> => {
    await apiClient.post('/contacts/bulk', operation);
  },

  /**
   * Export contacts
   */
  exportContacts: async (criteria: ContactSearchCriteria = {}): Promise<Blob> => {
    const params = new URLSearchParams();
    
    if (criteria.transactionId) params.append('transactionId', criteria.transactionId);
    if (criteria.role && criteria.role.length > 0) {
      criteria.role.forEach(role => params.append('role', role));
    }
    if (criteria.company) params.append('company', criteria.company);
    if (criteria.search) params.append('search', criteria.search);

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/contacts/export?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error('Failed to export contacts');
    }

    return response.blob();
  },

  /**
   * Search contacts with autocomplete
   */
  searchContacts: async (query: string, transactionId?: string): Promise<ContactSummary[]> => {
    const params = new URLSearchParams();
    params.append('search', query);
    params.append('limit', '10');
    if (transactionId) params.append('transactionId', transactionId);

    const response = await apiClient.get<PaginatedResponse<ContactSummary>>(
      `/contacts?${params.toString()}`
    );
    
    return response.data.items;
  },

  /**
   * Get contact suggestions for mentions
   */
  getContactSuggestions: async (transactionId: string): Promise<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  }[]> => {
    const response = await apiClient.get<ContactSummary[]>(`/contacts/transaction/${transactionId}`);
    
    return response.data.map(contact => ({
      id: contact.id,
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email || '',
    }));
  },
};

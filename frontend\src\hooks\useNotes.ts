/**
 * React Query hooks for note management
 */

import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { noteApi } from '@/lib/api';
import {
  Note,
  NoteSummary,
  CreateNoteData,
  UpdateNoteData,
  NoteSearchCriteria,
  NoteListResponse,
  NoteStats,
  NoteMention,
  NoteThread,
  BulkNoteOperation,
} from '@/types/note';

/**
 * Query keys for note-related queries
 */
export const noteKeys = {
  all: ['notes'] as const,
  lists: () => [...noteKeys.all, 'list'] as const,
  list: (criteria: NoteSearchCriteria) => [...noteKeys.lists(), criteria] as const,
  details: () => [...noteKeys.all, 'detail'] as const,
  detail: (id: string) => [...noteKeys.details(), id] as const,
  stats: (transactionId?: string) => [...noteKeys.all, 'stats', transactionId] as const,
  byTransaction: (transactionId: string) => [...noteKeys.all, 'transaction', transactionId] as const,
  thread: (transactionId: string) => [...noteKeys.all, 'thread', transactionId] as const,
  mentions: (userId: string, unreadOnly?: boolean) => [...noteKeys.all, 'mentions', userId, unreadOnly] as const,
  mentionableUsers: (transactionId: string) => [...noteKeys.all, 'mentionable-users', transactionId] as const,
  recentActivity: (limit?: number) => [...noteKeys.all, 'recent-activity', limit] as const,
  unreadCount: (userId: string) => [...noteKeys.all, 'unread-count', userId] as const,
};

/**
 * Hook to get notes with search and pagination
 */
export function useNotes(criteria: NoteSearchCriteria = {}) {
  return useQuery({
    queryKey: noteKeys.list(criteria),
    queryFn: () => noteApi.getNotes(criteria),
    keepPreviousData: true,
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for real-time feel)
  });
}

/**
 * Hook to get a single note by ID
 */
export function useNote(id: string, enabled = true) {
  return useQuery({
    queryKey: noteKeys.detail(id),
    queryFn: () => noteApi.getNote(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get note statistics
 */
export function useNoteStats(transactionId?: string) {
  return useQuery({
    queryKey: noteKeys.stats(transactionId),
    queryFn: () => noteApi.getNoteStats(transactionId),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get notes by transaction
 */
export function useNotesByTransaction(transactionId: string, enabled = true) {
  return useQuery({
    queryKey: noteKeys.byTransaction(transactionId),
    queryFn: () => noteApi.getNotesByTransaction(transactionId),
    enabled: enabled && !!transactionId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for real-time updates
  });
}

/**
 * Hook to get note thread for transaction
 */
export function useNoteThread(transactionId: string, enabled = true) {
  return useQuery({
    queryKey: noteKeys.thread(transactionId),
    queryFn: () => noteApi.getNoteThread(transactionId),
    enabled: enabled && !!transactionId,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 15 * 1000, // Refetch every 15 seconds for real-time chat
  });
}

/**
 * Hook to get user mentions
 */
export function useUserMentions(userId: string, unreadOnly = false, enabled = true) {
  return useQuery({
    queryKey: noteKeys.mentions(userId, unreadOnly),
    queryFn: () => noteApi.getUserMentions(userId, unreadOnly),
    enabled: enabled && !!userId,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for notifications
  });
}

/**
 * Hook to get mentionable users for a transaction
 */
export function useMentionableUsers(transactionId: string, enabled = true) {
  return useQuery({
    queryKey: noteKeys.mentionableUsers(transactionId),
    queryFn: () => noteApi.getMentionableUsers(transactionId),
    enabled: enabled && !!transactionId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get recent activity
 */
export function useRecentActivity(limit = 10) {
  return useQuery({
    queryKey: noteKeys.recentActivity(limit),
    queryFn: () => noteApi.getRecentActivity(limit),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // Refetch every minute for dashboard
  });
}

/**
 * Hook to get unread mentions count
 */
export function useUnreadMentionsCount(userId: string, enabled = true) {
  return useQuery({
    queryKey: noteKeys.unreadCount(userId),
    queryFn: () => noteApi.getUnreadMentionsCount(userId),
    enabled: enabled && !!userId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for real-time count
  });
}

/**
 * Hook to create a new note
 */
export function useCreateNote() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateNoteData) => noteApi.createNote(data),
    onSuccess: (newNote) => {
      // Invalidate and refetch note lists
      queryClient.invalidateQueries(noteKeys.lists());
      queryClient.invalidateQueries(noteKeys.stats());
      queryClient.invalidateQueries(noteKeys.byTransaction(newNote.transactionId));
      queryClient.invalidateQueries(noteKeys.thread(newNote.transactionId));
      queryClient.invalidateQueries(noteKeys.recentActivity());

      // Invalidate mentions for mentioned users
      if (newNote.mentions && newNote.mentions.length > 0) {
        newNote.mentions.forEach(userId => {
          queryClient.invalidateQueries(noteKeys.mentions(userId));
          queryClient.invalidateQueries(noteKeys.unreadCount(userId));
        });
      }

      toast.success('Note added successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create note');
    },
  });
}

/**
 * Hook to update a note
 */
export function useUpdateNote() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateNoteData }) =>
      noteApi.updateNote(id, data),
    onSuccess: (updatedNote) => {
      // Update the note in the cache
      queryClient.setQueryData(noteKeys.detail(updatedNote.id), updatedNote);
      
      // Invalidate related queries
      queryClient.invalidateQueries(noteKeys.lists());
      queryClient.invalidateQueries(noteKeys.stats());
      queryClient.invalidateQueries(noteKeys.byTransaction(updatedNote.transactionId));
      queryClient.invalidateQueries(noteKeys.thread(updatedNote.transactionId));

      // Invalidate mentions for mentioned users
      if (updatedNote.mentions && updatedNote.mentions.length > 0) {
        updatedNote.mentions.forEach(userId => {
          queryClient.invalidateQueries(noteKeys.mentions(userId));
          queryClient.invalidateQueries(noteKeys.unreadCount(userId));
        });
      }

      toast.success('Note updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update note');
    },
  });
}

/**
 * Hook to delete a note
 */
export function useDeleteNote() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => noteApi.deleteNote(id),
    onSuccess: (_, deletedId) => {
      // Remove the note from the cache
      queryClient.removeQueries(noteKeys.detail(deletedId));
      
      // Invalidate related queries
      queryClient.invalidateQueries(noteKeys.lists());
      queryClient.invalidateQueries(noteKeys.stats());
      queryClient.invalidateQueries(noteKeys.all);

      toast.success('Note deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete note');
    },
  });
}

/**
 * Hook to mark mention as read
 */
export function useMarkMentionAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ noteId, userId }: { noteId: string; userId: string }) =>
      noteApi.markMentionAsRead(noteId, userId),
    onSuccess: (_, { userId }) => {
      // Invalidate mention queries
      queryClient.invalidateQueries(noteKeys.mentions(userId));
      queryClient.invalidateQueries(noteKeys.unreadCount(userId));
    },
    onError: (error: any) => {
      console.error('Failed to mark mention as read:', error);
    },
  });
}

/**
 * Hook to perform bulk operations on notes
 */
export function useBulkNoteOperation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (operation: BulkNoteOperation) => noteApi.bulkOperation(operation),
    onSuccess: (_, operation) => {
      // Invalidate all note queries
      queryClient.invalidateQueries(noteKeys.all);

      const operationNames = {
        delete: 'deleted',
        export: 'exported',
      };

      const operationName = operationNames[operation.operation] || 'processed';
      toast.success(`${operation.noteIds.length} note(s) ${operationName} successfully`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to perform bulk operation');
    },
  });
}

/**
 * Hook to export notes
 */
export function useExportNotes() {
  return useMutation({
    mutationFn: (criteria: NoteSearchCriteria) => noteApi.exportNotes(criteria),
    onSuccess: (blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `notes-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Notes exported successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to export notes');
    },
  });
}

/**
 * Hook to search notes with debouncing
 */
export function useSearchNotes(query: string, transactionId?: string, enabled = true) {
  return useQuery({
    queryKey: [...noteKeys.all, 'search', query, transactionId],
    queryFn: () => noteApi.searchNotes(query, transactionId),
    enabled: enabled && query.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
}

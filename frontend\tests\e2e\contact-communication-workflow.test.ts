/**
 * End-to-End Contact & Communication Workflow Tests
 * 
 * Comprehensive E2E tests for the complete contact and communication system
 */

import { test, expect, Page } from '@playwright/test';

// Test data
const testContact = {
  firstName: 'John',
  lastName: '<PERSON>e',
  email: '<EMAIL>',
  phone: '555-0123',
  role: 'buyer',
  company: 'Test Company',
};

const testNote = {
  content: 'This is a test note for the transaction.',
};

const testMentionNote = {
  content: 'Hey @<PERSON> Doe, please review the inspection report.',
};

test.describe('Contact & Communication Management E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the demo page
    await page.goto('/demo/contact-communication');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('Contact Management Workflow', () => {
    test('should create, edit, and delete a contact', async ({ page }) => {
      // Test contact creation
      await test.step('Create new contact', async () => {
        // Click add contact button
        await page.click('[data-testid="add-contact-button"]');
        
        // Fill contact form
        await page.fill('[data-testid="contact-first-name"]', testContact.firstName);
        await page.fill('[data-testid="contact-last-name"]', testContact.lastName);
        await page.fill('[data-testid="contact-email"]', testContact.email);
        await page.fill('[data-testid="contact-phone"]', testContact.phone);
        await page.selectOption('[data-testid="contact-role"]', testContact.role);
        await page.fill('[data-testid="contact-company"]', testContact.company);
        
        // Submit form
        await page.click('[data-testid="save-contact-button"]');
        
        // Verify contact appears in list
        await expect(page.locator(`text=${testContact.firstName} ${testContact.lastName}`)).toBeVisible();
        await expect(page.locator(`text=${testContact.email}`)).toBeVisible();
      });

      // Test contact editing
      await test.step('Edit contact', async () => {
        // Click edit button for the contact
        await page.click('[data-testid="edit-contact-button"]');
        
        // Update contact information
        await page.fill('[data-testid="contact-phone"]', '555-9999');
        await page.fill('[data-testid="contact-company"]', 'Updated Company');
        
        // Submit form
        await page.click('[data-testid="save-contact-button"]');
        
        // Verify updates
        await expect(page.locator('text=555-9999')).toBeVisible();
        await expect(page.locator('text=Updated Company')).toBeVisible();
      });

      // Test contact deletion
      await test.step('Delete contact', async () => {
        // Click delete button
        await page.click('[data-testid="delete-contact-button"]');
        
        // Confirm deletion
        await page.click('[data-testid="confirm-delete-button"]');
        
        // Verify contact is removed
        await expect(page.locator(`text=${testContact.firstName} ${testContact.lastName}`)).not.toBeVisible();
      });
    });

    test('should search and filter contacts', async ({ page }) => {
      // Create multiple contacts first
      await createMultipleContacts(page);

      // Test search functionality
      await test.step('Search contacts', async () => {
        await page.fill('[data-testid="contact-search"]', 'John');
        
        // Verify search results
        await expect(page.locator('text=John Doe')).toBeVisible();
        await expect(page.locator('text=Jane Smith')).not.toBeVisible();
      });

      // Test role filter
      await test.step('Filter by role', async () => {
        await page.selectOption('[data-testid="role-filter"]', 'buyer');
        
        // Verify filtered results
        await expect(page.locator('[data-testid="contact-role"]:has-text("buyer")')).toBeVisible();
        await expect(page.locator('[data-testid="contact-role"]:has-text("seller")')).not.toBeVisible();
      });

      // Clear filters
      await page.fill('[data-testid="contact-search"]', '');
      await page.selectOption('[data-testid="role-filter"]', 'all');
    });
  });

  test.describe('Communication Workflow', () => {
    test('should create, edit, and delete notes', async ({ page }) => {
      // Test note creation
      await test.step('Create new note', async () => {
        // Click add note button
        await page.click('[data-testid="add-note-button"]');
        
        // Fill note content
        await page.fill('[data-testid="note-content"]', testNote.content);
        
        // Submit form
        await page.click('[data-testid="save-note-button"]');
        
        // Verify note appears
        await expect(page.locator(`text=${testNote.content}`)).toBeVisible();
      });

      // Test note editing
      await test.step('Edit note', async () => {
        // Click edit button
        await page.click('[data-testid="edit-note-button"]');
        
        // Update content
        const updatedContent = 'Updated note content';
        await page.fill('[data-testid="note-content"]', updatedContent);
        
        // Submit form
        await page.click('[data-testid="save-note-button"]');
        
        // Verify update
        await expect(page.locator(`text=${updatedContent}`)).toBeVisible();
      });

      // Test note deletion
      await test.step('Delete note', async () => {
        // Click delete button
        await page.click('[data-testid="delete-note-button"]');
        
        // Confirm deletion
        await page.click('[data-testid="confirm-delete-button"]');
        
        // Verify note is removed
        await expect(page.locator(`text=${testNote.content}`)).not.toBeVisible();
      });
    });

    test('should handle mentions correctly', async ({ page }) => {
      // Create a contact first
      await createTestContact(page);

      // Test mention creation
      await test.step('Create note with mention', async () => {
        // Click add note button
        await page.click('[data-testid="add-note-button"]');
        
        // Type @ to trigger mention dropdown
        await page.fill('[data-testid="note-content"]', 'Hey @');
        
        // Wait for mention dropdown
        await expect(page.locator('[data-testid="mention-dropdown"]')).toBeVisible();
        
        // Select user from dropdown
        await page.click('[data-testid="mention-option"]:has-text("John Doe")');
        
        // Complete the note
        await page.fill('[data-testid="note-content"]', testMentionNote.content);
        
        // Submit form
        await page.click('[data-testid="save-note-button"]');
        
        // Verify mention highlighting
        await expect(page.locator('[data-testid="mention-highlight"]')).toBeVisible();
      });

      // Test mention notifications
      await test.step('Verify mention notifications', async () => {
        // Check notification bell
        await expect(page.locator('[data-testid="notification-count"]')).toBeVisible();
        
        // Click notification bell
        await page.click('[data-testid="notification-bell"]');
        
        // Verify mention appears in dropdown
        await expect(page.locator('[data-testid="mention-notification"]')).toBeVisible();
      });
    });
  });

  test.describe('Dashboard Integration', () => {
    test('should display communication widgets on dashboard', async ({ page }) => {
      // Navigate to dashboard
      await page.goto('/dashboard');
      
      // Verify communication widgets are present
      await expect(page.locator('[data-testid="recent-communication"]')).toBeVisible();
      await expect(page.locator('[data-testid="quick-communication"]')).toBeVisible();
      await expect(page.locator('[data-testid="communication-stats"]')).toBeVisible();
      await expect(page.locator('[data-testid="notification-bell"]')).toBeVisible();
    });

    test('should navigate from dashboard to transaction communication', async ({ page }) => {
      // Navigate to dashboard
      await page.goto('/dashboard');
      
      // Click on a transaction link
      await page.click('[data-testid="transaction-link"]');
      
      // Verify navigation to transaction page
      await expect(page.url()).toContain('/dashboard/transactions/');
      
      // Click communication tab
      await page.click('[data-testid="communication-tab"]');
      
      // Verify communication interface is loaded
      await expect(page.locator('[data-testid="note-management"]')).toBeVisible();
    });
  });

  test.describe('Transaction Integration', () => {
    test('should manage contacts within transaction context', async ({ page }) => {
      // Navigate to transaction detail page
      await page.goto('/dashboard/transactions/trans-001');
      
      // Click contacts tab
      await page.click('[data-testid="contacts-tab"]');
      
      // Verify contact management interface
      await expect(page.locator('[data-testid="contact-management"]')).toBeVisible();
      
      // Create contact within transaction
      await page.click('[data-testid="add-contact-button"]');
      await page.fill('[data-testid="contact-first-name"]', 'Transaction');
      await page.fill('[data-testid="contact-last-name"]', 'Contact');
      await page.fill('[data-testid="contact-email"]', '<EMAIL>');
      await page.selectOption('[data-testid="contact-role"]', 'agent');
      await page.click('[data-testid="save-contact-button"]');
      
      // Verify contact is scoped to transaction
      await expect(page.locator('text=Transaction Contact')).toBeVisible();
    });

    test('should communicate within transaction context', async ({ page }) => {
      // Navigate to transaction detail page
      await page.goto('/dashboard/transactions/trans-001');
      
      // Click communication tab
      await page.click('[data-testid="communication-tab"]');
      
      // Verify note management interface
      await expect(page.locator('[data-testid="note-management"]')).toBeVisible();
      
      // Create note within transaction
      await page.click('[data-testid="add-note-button"]');
      await page.fill('[data-testid="note-content"]', 'Transaction-specific note');
      await page.click('[data-testid="save-note-button"]');
      
      // Verify note is scoped to transaction
      await expect(page.locator('text=Transaction-specific note')).toBeVisible();
    });
  });

  test.describe('Responsive Design', () => {
    test('should work correctly on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Navigate to demo page
      await page.goto('/demo/contact-communication');
      
      // Test mobile navigation
      await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
      
      // Test contact management on mobile
      await page.click('[data-testid="add-contact-button"]');
      await expect(page.locator('[data-testid="contact-form"]')).toBeVisible();
      
      // Test form interactions on mobile
      await page.fill('[data-testid="contact-first-name"]', 'Mobile');
      await page.fill('[data-testid="contact-last-name"]', 'User');
      await page.fill('[data-testid="contact-email"]', '<EMAIL>');
      
      // Verify mobile-optimized layout
      await expect(page.locator('[data-testid="contact-form"]')).toHaveCSS('width', /100%|375px/);
    });
  });

  test.describe('Performance', () => {
    test('should load quickly and handle large datasets', async ({ page }) => {
      // Measure page load time
      const startTime = Date.now();
      await page.goto('/demo/contact-communication');
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      // Verify reasonable load time (< 3 seconds)
      expect(loadTime).toBeLessThan(3000);
      
      // Test with large dataset
      await createLargeDataset(page);
      
      // Verify pagination works
      await expect(page.locator('[data-testid="pagination"]')).toBeVisible();
      
      // Test search performance with large dataset
      await page.fill('[data-testid="contact-search"]', 'test');
      await page.waitForTimeout(500); // Debounce delay
      
      // Verify search results load quickly
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    });
  });
});

// Helper functions
async function createTestContact(page: Page) {
  await page.click('[data-testid="add-contact-button"]');
  await page.fill('[data-testid="contact-first-name"]', testContact.firstName);
  await page.fill('[data-testid="contact-last-name"]', testContact.lastName);
  await page.fill('[data-testid="contact-email"]', testContact.email);
  await page.selectOption('[data-testid="contact-role"]', testContact.role);
  await page.click('[data-testid="save-contact-button"]');
}

async function createMultipleContacts(page: Page) {
  const contacts = [
    { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', role: 'buyer' },
    { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', role: 'seller' },
    { firstName: 'Bob', lastName: 'Johnson', email: '<EMAIL>', role: 'agent' },
  ];

  for (const contact of contacts) {
    await page.click('[data-testid="add-contact-button"]');
    await page.fill('[data-testid="contact-first-name"]', contact.firstName);
    await page.fill('[data-testid="contact-last-name"]', contact.lastName);
    await page.fill('[data-testid="contact-email"]', contact.email);
    await page.selectOption('[data-testid="contact-role"]', contact.role);
    await page.click('[data-testid="save-contact-button"]');
    await page.waitForTimeout(500); // Wait between creations
  }
}

async function createLargeDataset(page: Page) {
  // This would typically be done via API calls for performance
  // For demo purposes, we'll simulate it
  await page.evaluate(() => {
    // Mock large dataset in localStorage or via API
    const largeDataset = Array.from({ length: 100 }, (_, i) => ({
      id: `contact-${i}`,
      firstName: `Contact${i}`,
      lastName: `User${i}`,
      email: `contact${i}@example.com`,
      role: ['buyer', 'seller', 'agent'][i % 3],
    }));
    
    localStorage.setItem('mockContacts', JSON.stringify(largeDataset));
  });
  
  await page.reload();
}

/**
 * DashboardHeader component
 * 
 * Enhanced dashboard header with communication features
 */

'use client';

import Link from 'next/link';
import { useAuthStore } from '@/store/authStore';
import { NotificationBell } from './NotificationBell';
import { Button } from '@/components/ui/Button';

export function DashboardHeader() {
  const { user } = useAuthStore();

  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left side - Logo and navigation */}
          <div className="flex items-center space-x-8">
            <div className="flex-shrink-0">
              <Link href="/dashboard" className="text-xl font-bold text-blue-600">
                TC Platform
              </Link>
            </div>
            
            <nav className="hidden md:flex space-x-6">
              <Link
                href="/dashboard"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
              >
                Dashboard
              </Link>
              <Link
                href="/dashboard/transactions"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
              >
                Transactions
              </Link>
              <Link
                href="/demo/contact-communication"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
              >
                Communication
              </Link>
            </nav>
          </div>

          {/* Right side - Actions and user menu */}
          <div className="flex items-center space-x-4">
            {/* Quick Actions */}
            <div className="hidden md:flex items-center space-x-2">
              <Button variant="outline" size="sm">
                💬 New Message
              </Button>
              <Button size="sm">
                ➕ New Transaction
              </Button>
            </div>

            {/* Notifications */}
            <NotificationBell currentUserId={user?.id} />

            {/* User Menu */}
            <div className="flex items-center space-x-3">
              <div className="hidden md:block text-right">
                <p className="text-sm font-medium text-gray-900">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-gray-500">{user?.role}</p>
              </div>
              
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

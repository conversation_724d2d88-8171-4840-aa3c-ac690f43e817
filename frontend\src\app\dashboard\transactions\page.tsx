/**
 * Transactions List Page
 * 
 * Lists all transactions with navigation to detail pages
 */

'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';

interface Transaction {
  id: string;
  propertyAddress: string;
  transactionType: 'PURCHASE' | 'SALE' | 'LEASE';
  status: 'PENDING' | 'ACTIVE' | 'UNDER_CONTRACT' | 'CLOSED' | 'CANCELLED';
  contractDate?: Date;
  closingDate?: Date;
  salePrice?: number;
  buyerName?: string;
  sellerName?: string;
  tc?: {
    firstName: string;
    lastName: string;
  };
  taskCount: number;
  completedTaskCount: number;
  contactCount: number;
  noteCount: number;
}

export default function TransactionsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock transactions data
  const mockTransactions: Transaction[] = [
    {
      id: 'trans-001',
      propertyAddress: '123 Main Street, Philadelphia, PA 19103',
      transactionType: 'PURCHASE',
      status: 'ACTIVE',
      contractDate: new Date('2024-01-01'),
      closingDate: new Date('2024-02-15'),
      salePrice: 450000,
      buyerName: 'John & Jane Smith',
      sellerName: 'Robert Johnson',
      tc: { firstName: 'Sarah', lastName: 'Wilson' },
      taskCount: 15,
      completedTaskCount: 8,
      contactCount: 6,
      noteCount: 23,
    },
    {
      id: 'trans-002',
      propertyAddress: '456 Oak Avenue, Philadelphia, PA 19104',
      transactionType: 'SALE',
      status: 'UNDER_CONTRACT',
      contractDate: new Date('2024-01-15'),
      closingDate: new Date('2024-03-01'),
      salePrice: 325000,
      buyerName: 'Michael Chen',
      sellerName: 'Lisa Rodriguez',
      tc: { firstName: 'Mike', lastName: 'Johnson' },
      taskCount: 12,
      completedTaskCount: 10,
      contactCount: 4,
      noteCount: 18,
    },
    {
      id: 'trans-003',
      propertyAddress: '789 Pine Street, Philadelphia, PA 19102',
      transactionType: 'PURCHASE',
      status: 'PENDING',
      contractDate: new Date('2024-02-01'),
      closingDate: new Date('2024-03-15'),
      salePrice: 275000,
      buyerName: 'David & Sarah Kim',
      sellerName: 'Thomas Wilson',
      tc: { firstName: 'Jennifer', lastName: 'Davis' },
      taskCount: 8,
      completedTaskCount: 2,
      contactCount: 5,
      noteCount: 7,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'yellow';
      case 'ACTIVE':
        return 'blue';
      case 'UNDER_CONTRACT':
        return 'purple';
      case 'CLOSED':
        return 'green';
      case 'CANCELLED':
        return 'red';
      default:
        return 'gray';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };

  const filteredTransactions = mockTransactions.filter(transaction => {
    const matchesSearch = transaction.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.buyerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.sellerName?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Transactions</h1>
                <p className="mt-2 text-gray-600">
                  Manage your real estate transactions
                </p>
              </div>
              <Button>
                ➕ New Transaction
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Statuses</option>
              <option value="PENDING">Pending</option>
              <option value="ACTIVE">Active</option>
              <option value="UNDER_CONTRACT">Under Contract</option>
              <option value="CLOSED">Closed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>
        </div>
      </div>

      {/* Transactions List */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="space-y-4">
          {filteredTransactions.map((transaction) => {
            const progressPercentage = Math.round((transaction.completedTaskCount / transaction.taskCount) * 100);
            
            return (
              <Link
                key={transaction.id}
                href={`/dashboard/transactions/${transaction.id}`}
                className="block bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    {/* Left side - Transaction info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {transaction.propertyAddress}
                        </h3>
                        <Badge color={getStatusColor(transaction.status)}>
                          {transaction.status.replace('_', ' ')}
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-3">
                        <div>
                          <span className="font-medium">Type:</span> {transaction.transactionType}
                        </div>
                        {transaction.salePrice && (
                          <div>
                            <span className="font-medium">Price:</span> {formatCurrency(transaction.salePrice)}
                          </div>
                        )}
                        {transaction.closingDate && (
                          <div>
                            <span className="font-medium">Closing:</span> {formatDate(transaction.closingDate)}
                          </div>
                        )}
                      </div>

                      <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600">
                        {transaction.buyerName && (
                          <div>
                            <span className="font-medium">Buyer:</span> {transaction.buyerName}
                          </div>
                        )}
                        {transaction.tc && (
                          <div>
                            <span className="font-medium">TC:</span> {transaction.tc.firstName} {transaction.tc.lastName}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Right side - Stats and progress */}
                    <div className="flex items-center space-x-6 ml-6">
                      {/* Quick stats */}
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="text-center">
                          <div className="font-semibold text-gray-900">{transaction.contactCount}</div>
                          <div>Contacts</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-gray-900">{transaction.noteCount}</div>
                          <div>Notes</div>
                        </div>
                      </div>

                      {/* Progress */}
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900">
                          {progressPercentage}%
                        </div>
                        <div className="text-xs text-gray-500 mb-1">
                          {transaction.completedTaskCount}/{transaction.taskCount} tasks
                        </div>
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${progressPercentage}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {filteredTransactions.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">No transactions found</div>
            <Button>Create Your First Transaction</Button>
          </div>
        )}
      </div>
    </div>
  );
}

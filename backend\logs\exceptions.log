2025-05-24 21:43:22 [[31merror[39m]: uncaughtException: Cannot read properties of undefined (reading 'required')
TypeError: Cannot read properties of undefined (reading 'required')
    at Object.<anonymous> (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\src\utils\validation.ts:338:36)
    at Module._compile (node:internal/modules/cjs/loader:1469:14)
    at Module._compile (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\source-map-support\source-map-support.js:521:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:71:20)
    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1104:12)
TypeError: Cannot read properties of undefined (reading 'required')
    at Object.<anonymous> (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\src\utils\validation.ts:338:36)
    at Module._compile (node:internal/modules/cjs/loader:1469:14)
    at Module._compile (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\source-map-support\source-map-support.js:521:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:71:20)
    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1104:12)
{
  "error": {},
  "exception": true,
  "date": "Sat May 24 2025 21:43:22 GMT-0400 (Eastern Daylight Time)",
  "process": {
    "pid": 28412,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend",
    "execPath": "C:\\nvm4w\\nodejs\\node.exe",
    "version": "v20.18.2",
    "argv": [
      "C:\\nvm4w\\nodejs\\node.exe",
      "src/index.ts"
    ],
    "memoryUsage": {
      "rss": 78168064,
      "heapTotal": 33722368,
      "heapUsed": 23122984,
      "external": 2268230,
      "arrayBuffers": 137035
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 24337.468
  },
  "trace": [
    {
      "column": 36,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\src\\utils\\validation.ts",
      "function": null,
      "line": 338,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1469,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 521,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8893199701380339.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1548,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8893199701380339.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8893199701380339.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1288,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1104,
      "method": "_load",
      "native": false
    }
  ],
  "service": "tc-platform-api",
  "version": "1.0.0"
}
2025-05-24 21:44:14 [[31merror[39m]: uncaughtException: Cannot read properties of undefined (reading 'required')
TypeError: Cannot read properties of undefined (reading 'required')
    at Object.<anonymous> (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\src\routes\documents.ts:34:34)
    at Module._compile (node:internal/modules/cjs/loader:1469:14)
    at Module._compile (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\source-map-support\source-map-support.js:521:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:71:20)
    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1104:12)
TypeError: Cannot read properties of undefined (reading 'required')
    at Object.<anonymous> (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\src\routes\documents.ts:34:34)
    at Module._compile (node:internal/modules/cjs/loader:1469:14)
    at Module._compile (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\source-map-support\source-map-support.js:521:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-8893199701380339.js:71:20)
    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1104:12)
{
  "error": {},
  "exception": true,
  "date": "Sat May 24 2025 21:44:14 GMT-0400 (Eastern Daylight Time)",
  "process": {
    "pid": 21652,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend",
    "execPath": "C:\\nvm4w\\nodejs\\node.exe",
    "version": "v20.18.2",
    "argv": [
      "C:\\nvm4w\\nodejs\\node.exe",
      "src/index.ts"
    ],
    "memoryUsage": {
      "rss": 81317888,
      "heapTotal": 52858880,
      "heapUsed": 17972920,
      "external": 2256975,
      "arrayBuffers": 117015
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 24389.25
  },
  "trace": [
    {
      "column": 34,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\src\\routes\\documents.ts",
      "function": null,
      "line": 34,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1469,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 521,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8893199701380339.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1548,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8893199701380339.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8893199701380339.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1288,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1104,
      "method": "_load",
      "native": false
    }
  ],
  "service": "tc-platform-api",
  "version": "1.0.0"
}
2025-05-24 21:44:46 [[31merror[39m]: uncaughtException: Cannot read properties of undefined (reading 'required')
TypeError: Cannot read properties of undefined (reading 'required')
    at Object.<anonymous> (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\src\routes\documents.ts:34:34)
    at Module._compile (node:internal/modules/cjs/loader:1469:14)
    at Module._compile (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\source-map-support\source-map-support.js:521:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4265080000145993.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4265080000145993.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4265080000145993.js:71:20)
    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1104:12)
TypeError: Cannot read properties of undefined (reading 'required')
    at Object.<anonymous> (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\src\routes\documents.ts:34:34)
    at Module._compile (node:internal/modules/cjs/loader:1469:14)
    at Module._compile (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\source-map-support\source-map-support.js:521:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4265080000145993.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4265080000145993.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4265080000145993.js:71:20)
    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\OneDrive\Documents\Development\tcb2b\backend\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1104:12)
{
  "error": {},
  "exception": true,
  "date": "Sat May 24 2025 21:44:46 GMT-0400 (Eastern Daylight Time)",
  "process": {
    "pid": 18480,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend",
    "execPath": "C:\\nvm4w\\nodejs\\node.exe",
    "version": "v20.18.2",
    "argv": [
      "C:\\nvm4w\\nodejs\\node.exe",
      "src/index.ts"
    ],
    "memoryUsage": {
      "rss": 80670720,
      "heapTotal": 35295232,
      "heapUsed": 24085048,
      "external": 2256975,
      "arrayBuffers": 137842
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 24421.703
  },
  "trace": [
    {
      "column": 34,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\src\\routes\\documents.ts",
      "function": null,
      "line": 34,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1469,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 521,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4265080000145993.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1548,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4265080000145993.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4265080000145993.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Development\\tcb2b\\backend\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1288,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1104,
      "method": "_load",
      "native": false
    }
  ],
  "service": "tc-platform-api",
  "version": "1.0.0"
}

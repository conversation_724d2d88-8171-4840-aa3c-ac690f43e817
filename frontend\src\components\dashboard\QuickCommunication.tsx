/**
 * QuickCommunication component
 * 
 * Dashboard widget for quick note creation and communication
 */

'use client';

import { useState } from 'react';
import { useCreateNote, useMentionableUsers } from '@/hooks';
import { MentionInput } from '@/components/notes/MentionInput';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';

interface QuickCommunicationProps {
  currentUserId?: string;
}

export function QuickCommunication({ currentUserId = 'user-1' }: QuickCommunicationProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState('');
  const [content, setContent] = useState('');
  const [mentions, setMentions] = useState<string[]>([]);

  // React Query hooks
  const createNoteMutation = useCreateNote();
  const { data: availableUsers = [] } = useMentionableUsers(selectedTransaction, !!selectedTransaction);

  // Mock transaction options
  const transactionOptions = [
    { value: 'trans-001', label: '123 Main St, Philadelphia, PA' },
    { value: 'trans-002', label: '456 Oak Ave, Philadelphia, PA' },
    { value: 'trans-003', label: '789 Pine St, Philadelphia, PA' },
  ];

  const handleContentChange = (newContent: string, newMentions: string[]) => {
    setContent(newContent);
    setMentions(newMentions);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim() || !selectedTransaction) {
      return;
    }

    try {
      await createNoteMutation.mutateAsync({
        transactionId: selectedTransaction,
        content: content.trim(),
        mentions,
      });

      // Reset form
      setContent('');
      setMentions([]);
      setIsExpanded(false);
      
    } catch (error) {
      console.error('Error creating note:', error);
    }
  };

  const handleCancel = () => {
    setContent('');
    setMentions([]);
    setIsExpanded(false);
  };

  if (!isExpanded) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Quick Communication
          </h3>
          
          <button
            onClick={() => setIsExpanded(true)}
            className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors group"
          >
            <div className="text-center">
              <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">💬</div>
              <p className="text-sm font-medium text-gray-900">Start a conversation</p>
              <p className="text-xs text-gray-500 mt-1">
                Send a quick note to your team
              </p>
            </div>
          </button>

          <div className="mt-4 grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedTransaction('trans-001');
                setIsExpanded(true);
              }}
            >
              📍 Quick Note
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('/demo/contact-communication', '_blank')}
            >
              🚀 Demo
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            New Message
          </h3>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Transaction Selection */}
          <div>
            <label htmlFor="transaction" className="block text-sm font-medium text-gray-700 mb-1">
              Transaction
            </label>
            <Select
              id="transaction"
              value={selectedTransaction}
              onChange={setSelectedTransaction}
              options={transactionOptions}
              placeholder="Select a transaction"
              required
            />
          </div>

          {/* Message Content */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
              Message
            </label>
            <MentionInput
              value={content}
              onChange={handleContentChange}
              placeholder="Type your message... Use @ to mention someone"
              availableUsers={availableUsers}
              maxLength={500}
            />
          </div>

          {/* Mentioned Users Preview */}
          {mentions.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <div className="text-sm font-medium text-blue-900 mb-2">
                Mentioning {mentions.length} user{mentions.length > 1 ? 's' : ''}:
              </div>
              <div className="flex flex-wrap gap-2">
                {availableUsers
                  .filter(user => mentions.includes(user.id))
                  .map(user => (
                    <div
                      key={user.id}
                      className="inline-flex items-center space-x-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"
                    >
                      <span>@{user.firstName} {user.lastName}</span>
                    </div>
                  ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={createNoteMutation.isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={createNoteMutation.isLoading}
              disabled={!content.trim() || !selectedTransaction || createNoteMutation.isLoading}
            >
              Send Message
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User roles enum
enum UserRole {
  TC // Transaction Coordinator
  AGENT // Real Estate Agent
  BROKER // Broker
  CLIENT // Client (Buyer/Seller)
  VENDOR // Service Vendor/Attorney
  ADMIN // Brokerage Admin
}

// Transaction status enum
enum TransactionStatus {
  PENDING
  UNDER_CONTRACT
  INSPECTION
  APPRAISAL
  FINANCING
  CLOSING
  CLOSED
  CANCELLED
}

// Transaction type enum
enum TransactionType {
  RESIDENTIAL
  COMMERCIAL
  RENTAL
  LAND
}

// Document category enum
enum DocumentCategory {
  CONTRACT
  INSPECTION
  APPRAISAL
  FINANCING
  INSURANCE
  TITLE
  CLOSING
  OTHER
}

// Task priority enum
enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// Brokerage model
model Brokerage {
  id        String   @id @default(cuid())
  name      String
  address   String?
  phone     String?
  email     String?
  settings  Json? // Store brokerage-specific settings
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users        User[]
  transactions Transaction[]

  @@map("brokerages")
}

// User model
model User {
  id           String    @id @default(cuid())
  email        String    @unique
  passwordHash String
  firstName    String
  lastName     String
  phone        String?
  role         UserRole
  brokerageId  String?
  isActive     Boolean   @default(true)
  lastLogin    DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  brokerage             Brokerage?    @relation(fields: [brokerageId], references: [id])
  transactionsAsTC      Transaction[] @relation("TransactionCoordinator")
  transactionsAsListing Transaction[] @relation("ListingAgent")
  transactionsAsSelling Transaction[] @relation("SellingAgent")
  assignedTasks         Task[]        @relation("AssignedUser")
  createdTasks          Task[]        @relation("CreatedByUser")
  uploadedDocuments     Document[]
  notes                 Note[]
  auditLogs             AuditLog[]

  @@map("users")
}

// Transaction model
model Transaction {
  id              String            @id @default(cuid())
  propertyAddress String
  transactionType TransactionType
  status          TransactionStatus @default(PENDING)
  contractDate    DateTime?
  closingDate     DateTime?
  salePrice       Decimal?          @db.Decimal(12, 2)

  // Parties
  buyerName  String?
  sellerName String?

  // Agents and TC
  tcId           String?
  listingAgentId String?
  sellingAgentId String?
  brokerageId    String

  // Metadata
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  brokerage    Brokerage @relation(fields: [brokerageId], references: [id])
  tc           User?     @relation("TransactionCoordinator", fields: [tcId], references: [id])
  listingAgent User?     @relation("ListingAgent", fields: [listingAgentId], references: [id])
  sellingAgent User?     @relation("SellingAgent", fields: [sellingAgentId], references: [id])

  tasks            Task[]
  documents        Document[]
  contacts         Contact[]
  transactionNotes Note[]
  auditLogs        AuditLog[]

  @@map("transactions")
}

// Task template model
model TaskTemplate {
  id              String          @id @default(cuid())
  name            String
  description     String?
  defaultDueDays  Int // Days from contract date
  transactionType TransactionType
  category        String?
  priority        TaskPriority    @default(MEDIUM)
  isRequired      Boolean         @default(false)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  tasks Task[]

  @@map("task_templates")
}

// Task model
model Task {
  id            String       @id @default(cuid())
  transactionId String
  title         String
  description   String?
  dueDate       DateTime
  completedAt   DateTime?
  priority      TaskPriority @default(MEDIUM)
  category      String?
  isRequired    Boolean      @default(false)

  // Assignment
  assignedToId   String?
  createdById    String
  taskTemplateId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  transaction  Transaction   @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  assignedTo   User?         @relation("AssignedUser", fields: [assignedToId], references: [id])
  createdBy    User          @relation("CreatedByUser", fields: [createdById], references: [id])
  taskTemplate TaskTemplate? @relation(fields: [taskTemplateId], references: [id])

  @@map("tasks")
}

// Document model
model Document {
  id               String           @id @default(cuid())
  transactionId    String
  filename         String
  originalFilename String
  filePath         String
  fileSize         Int
  mimeType         String
  category         DocumentCategory

  // E-signature integration
  isSigned           Boolean @default(false)
  docusignEnvelopeId String?

  // Upload info
  uploadedById String
  uploadedAt   DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  uploadedBy  User        @relation(fields: [uploadedById], references: [id])

  @@map("documents")
}

// Contact model
model Contact {
  id            String   @id @default(cuid())
  transactionId String
  firstName     String
  lastName      String
  email         String?
  phone         String?
  role          String // buyer, seller, attorney, inspector, etc.
  company       String?
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@map("contacts")
}

// Note model for collaboration
model Note {
  id            String   @id @default(cuid())
  transactionId String
  userId        String
  content       String
  mentions      String[] // Array of user IDs mentioned
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  user        User        @relation(fields: [userId], references: [id])

  @@map("notes")
}

// Audit log model for compliance
model AuditLog {
  id            String   @id @default(cuid())
  userId        String
  transactionId String?
  action        String // CREATE, UPDATE, DELETE, etc.
  entityType    String // Transaction, Task, Document, etc.
  entityId      String?
  details       Json? // Store additional details about the action
  ipAddress     String?
  userAgent     String?
  createdAt     DateTime @default(now())

  // Relations
  user        User         @relation(fields: [userId], references: [id])
  transaction Transaction? @relation(fields: [transactionId], references: [id])

  @@map("audit_logs")
}

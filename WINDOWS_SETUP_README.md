# Windows Automated Setup Guide

## 🚀 One-Click Setup Options

### Option 1: PowerShell Script (Recommended)
**Installs everything automatically including Node.js and PostgreSQL**

1. **Right-click** on PowerShell and select **"Run as Administrator"**
2. **Navigate** to the TC Platform directory:
   ```powershell
   cd "C:\path\to\tc-platform"
   ```
3. **Run the setup script**:
   ```powershell
   .\setup-windows.ps1
   ```

**What it does:**
- ✅ Installs Node.js 18+ via Chocolatey
- ✅ Installs PostgreSQL 15+ via Chocolatey  
- ✅ Creates database and user
- ✅ Installs all project dependencies
- ✅ Sets up environment files
- ✅ Runs database migrations
- ✅ Seeds sample data
- ✅ Creates startup scripts

### Option 2: Batch Script (Simple)
**For users who already have Node.js and PostgreSQL installed**

1. **Right-click** on `setup-windows.bat` and select **"Run as administrator"**
2. **Follow the prompts**

**Prerequisites:**
- Node.js 18+ installed
- PostgreSQL 14+ installed
- Both added to Windows PATH

### Option 3: Manual Setup
**If you prefer to do it step by step**

Follow the instructions in `SETUP_AND_RUN_GUIDE.md`

## 🎯 Quick Start (After Setup)

### Start the Application
**Double-click** `start-both.bat` or run:
```cmd
start-both.bat
```

This will open two command windows:
- Backend server (http://localhost:3001)
- Frontend server (http://localhost:3000)

### Access the Application
- **Main App**: http://localhost:3000
- **Dashboard**: http://localhost:3000/dashboard
- **Contact & Communication Demo**: http://localhost:3000/demo/contact-communication

## 🔧 Script Options

### PowerShell Script Options
```powershell
# Custom database password
.\setup-windows.ps1 -DatabasePassword "mypassword123"

# Custom JWT secret
.\setup-windows.ps1 -JwtSecret "my-super-secret-key"

# Skip Node.js/PostgreSQL installation (if already installed)
.\setup-windows.ps1 -SkipPrerequisites

# Show help
.\setup-windows.ps1 -Help
```

## 🐛 Troubleshooting

### Common Issues

**1. "Execution Policy" Error**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**2. "Not running as Administrator"**
- Right-click PowerShell/Command Prompt
- Select "Run as administrator"

**3. "PostgreSQL service won't start"**
```cmd
net start postgresql-x64-15
```

**4. "Database connection failed"**
- Check PostgreSQL is running
- Verify password in .env file
- Try connecting manually: `psql -U tc_user -d tc_platform`

**5. "Port already in use"**
```cmd
# Kill processes on ports 3000 and 3001
taskkill /f /im node.exe
```

**6. "Node modules error"**
```cmd
cd backend
rmdir /s node_modules
npm install

cd ../frontend  
rmdir /s node_modules
npm install
```

### Manual PostgreSQL Setup
If automatic setup fails:

1. **Install PostgreSQL** from https://www.postgresql.org/download/windows/
2. **Open SQL Shell (psql)**
3. **Run these commands**:
   ```sql
   CREATE DATABASE tc_platform;
   CREATE USER tc_user WITH PASSWORD 'tc_password_2024';
   GRANT ALL PRIVILEGES ON DATABASE tc_platform TO tc_user;
   ALTER USER tc_user CREATEDB;
   ```

### Manual Node.js Setup
If Node.js installation fails:

1. **Download** from https://nodejs.org/ (LTS version)
2. **Install** with default options
3. **Restart** command prompt
4. **Verify**: `node --version` and `npm --version`

## 📁 Generated Files

After setup, you'll have these new files:

### Environment Files
- `backend/.env` - Backend configuration
- `frontend/.env.local` - Frontend configuration

### Startup Scripts
- `start-backend.bat` - Start backend only
- `start-frontend.bat` - Start frontend only  
- `start-both.bat` - Start both services

### Database
- Database: `tc_platform`
- User: `tc_user`
- Password: `tc_password_2024` (or custom)

## 🔒 Security Notes

### Default Credentials
The setup creates default credentials for development:
- **Database Password**: `tc_password_2024`
- **JWT Secret**: Auto-generated random string

### For Production
Before deploying to production:
1. Change database password
2. Generate strong JWT secret
3. Update environment variables
4. Enable SSL/HTTPS
5. Configure firewall rules

## 🎯 What's Included

### Sample Data
The setup includes sample data for testing:

**Users:**
- <EMAIL> (TC)
- <EMAIL> (Agent)
- <EMAIL> (Agent)

**Transactions:**
- 123 Main St, Philadelphia, PA (Active Purchase)
- 456 Oak Ave, Philadelphia, PA (Under Contract Sale)
- 789 Pine St, Philadelphia, PA (Pending Purchase)

**Contacts:**
- John & Jane Smith (Buyers)
- Robert Johnson (Seller)
- Various agents and lenders

### Features Ready to Test
- ✅ Contact Management (CRUD operations)
- ✅ Real-time Communication with @mentions
- ✅ Dashboard with activity feeds
- ✅ Transaction integration
- ✅ Search and filtering
- ✅ Mobile-responsive design
- ✅ Notification system

## 🚀 Next Steps

1. **Start the application** using `start-both.bat`
2. **Open your browser** to http://localhost:3000
3. **Explore the dashboard** and transaction pages
4. **Test contact management** features
5. **Try the communication system** with @mentions
6. **Check out the demo** at `/demo/contact-communication`

## 📞 Support

If you encounter issues:

1. **Check the troubleshooting section** above
2. **Review the console output** for error messages
3. **Verify prerequisites** are properly installed
4. **Check Windows Event Viewer** for system errors
5. **Try manual setup** if automated setup fails

The TC Platform with Contact & Communication Management is now ready to use on Windows! 🎉

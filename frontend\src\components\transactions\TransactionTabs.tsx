/**
 * TransactionTabs component
 * 
 * Navigation tabs for transaction detail page
 */

'use client';

import { cn } from '@/lib/utils';

interface Tab {
  id: string;
  name: string;
  icon: string;
  count?: number;
}

interface TransactionTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export function TransactionTabs({ tabs, activeTab, onTabChange }: TransactionTabsProps) {
  return (
    <nav className="-mb-px flex space-x-8" aria-label="Tabs">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={cn(
            'py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center space-x-2',
            activeTab === tab.id
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          )}
        >
          <span>{tab.icon}</span>
          <span>{tab.name}</span>
          {tab.count !== undefined && (
            <span
              className={cn(
                'inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full',
                activeTab === tab.id
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-gray-100 text-gray-600'
              )}
            >
              {tab.count}
            </span>
          )}
        </button>
      ))}
    </nav>
  );
}

# Quick PostgreSQL Setup Fix for TC Platform
# Run this script as Administrator if the main setup is stuck

param(
    [string]$DatabasePassword = "tc_password_2024"
)

# Colors for output
$ErrorColor = "Red"
$SuccessColor = "Green"
$InfoColor = "Cyan"
$WarningColor = "Yellow"

function Write-Step {
    param([string]$Message)
    Write-Host ""
    Write-Host "🔄 $Message" -ForegroundColor $InfoColor
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor $SuccessColor
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor $ErrorColor
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor $WarningColor
}

function Get-PostgreSQLPath {
    # Try to find PostgreSQL installation
    $possiblePaths = @(
        "C:\Program Files\PostgreSQL\17\bin",
        "C:\Program Files\PostgreSQL\16\bin", 
        "C:\Program Files\PostgreSQL\15\bin",
        "C:\Program Files\PostgreSQL\14\bin"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path "$path\pg_isready.exe") {
            return $path
        }
    }
    
    return $null
}

Write-Host "🔧 PostgreSQL Setup Fix for TC Platform" -ForegroundColor $InfoColor
Write-Host "=======================================" -ForegroundColor $InfoColor

# 1. Check PostgreSQL installation
Write-Step "Checking PostgreSQL installation..."
$pgPath = Get-PostgreSQLPath
if (!$pgPath) {
    Write-Error "PostgreSQL not found. Please install PostgreSQL first."
    Write-Host "Download from: https://www.postgresql.org/download/windows/" -ForegroundColor $InfoColor
    exit 1
}
Write-Success "Found PostgreSQL at: $pgPath"

# 2. Check and start PostgreSQL service
Write-Step "Checking PostgreSQL service..."
$services = Get-Service postgresql* -ErrorAction SilentlyContinue
if ($services) {
    foreach ($service in $services) {
        Write-Host "Found service: $($service.Name) - Status: $($service.Status)" -ForegroundColor $InfoColor
        if ($service.Status -ne "Running") {
            try {
                Start-Service $service.Name
                Write-Success "Started service: $($service.Name)"
            }
            catch {
                Write-Warning "Could not start service: $($service.Name)"
            }
        } else {
            Write-Success "Service already running: $($service.Name)"
        }
    }
} else {
    Write-Warning "No PostgreSQL services found. You may need to start PostgreSQL manually."
}

# 3. Test PostgreSQL connection
Write-Step "Testing PostgreSQL connection..."
$maxAttempts = 10
$attempt = 0

do {
    $attempt++
    try {
        $result = & "$pgPath\pg_isready.exe" -h localhost -p 5432 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "PostgreSQL is ready and accepting connections"
            break
        }
    }
    catch {
        # Continue trying
    }
    
    if ($attempt -lt $maxAttempts) {
        Write-Host "Attempt $attempt/$maxAttempts - PostgreSQL not ready, waiting 3 seconds..." -ForegroundColor $WarningColor
        Start-Sleep -Seconds 3
    }
} while ($attempt -lt $maxAttempts)

if ($attempt -eq $maxAttempts) {
    Write-Error "PostgreSQL is not responding. Please check the installation."
    Write-Host ""
    Write-Host "Manual steps to try:" -ForegroundColor $InfoColor
    Write-Host "1. Open Services (services.msc)"
    Write-Host "2. Find PostgreSQL service and start it"
    Write-Host "3. Or run: net start postgresql-x64-17"
    exit 1
}

# 4. Create database and user
Write-Step "Setting up database and user..."
$sqlCommands = @"
-- Check if database exists
SELECT 1 FROM pg_database WHERE datname = 'tc_platform';
-- Create database if it doesn't exist
CREATE DATABASE tc_platform;
-- Check if user exists  
SELECT 1 FROM pg_roles WHERE rolname = 'tc_user';
-- Create user if it doesn't exist
CREATE USER tc_user WITH PASSWORD '$DatabasePassword';
GRANT ALL PRIVILEGES ON DATABASE tc_platform TO tc_user;
ALTER USER tc_user CREATEDB;
"@

try {
    # First, try to connect as postgres user
    Write-Host "Attempting to connect to PostgreSQL..." -ForegroundColor $InfoColor
    $env:PGPASSWORD = $DatabasePassword
    
    # Create a temporary SQL file
    $tempSqlFile = [System.IO.Path]::GetTempFileName() + ".sql"
    $sqlCommands | Out-File -FilePath $tempSqlFile -Encoding UTF8
    
    # Execute SQL commands
    $result = & "$pgPath\psql.exe" -U postgres -h localhost -f $tempSqlFile 2>&1
    
    # Clean up temp file
    Remove-Item $tempSqlFile -ErrorAction SilentlyContinue
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Database and user setup completed"
    } else {
        Write-Warning "Some database setup commands may have failed, but this might be normal if they already exist."
        Write-Host "Output: $result" -ForegroundColor $WarningColor
    }
}
catch {
    Write-Error "Failed to setup database: $($_.Exception.Message)"
    Write-Host ""
    Write-Host "Manual database setup:" -ForegroundColor $InfoColor
    Write-Host "1. Open Command Prompt as Administrator"
    Write-Host "2. Run: `"$pgPath\psql.exe`" -U postgres"
    Write-Host "3. Execute these commands:"
    Write-Host "   CREATE DATABASE tc_platform;"
    Write-Host "   CREATE USER tc_user WITH PASSWORD '$DatabasePassword';"
    Write-Host "   GRANT ALL PRIVILEGES ON DATABASE tc_platform TO tc_user;"
    Write-Host "   ALTER USER tc_user CREATEDB;"
}

# 5. Test connection with new user
Write-Step "Testing connection with tc_user..."
try {
    $env:PGPASSWORD = $DatabasePassword
    $result = & "$pgPath\psql.exe" -U tc_user -h localhost -d tc_platform -c "SELECT version();" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Successfully connected as tc_user"
    } else {
        Write-Warning "Could not connect as tc_user, but postgres user connection works"
    }
}
catch {
    Write-Warning "Could not test tc_user connection, but this may be normal"
}

Write-Host ""
Write-Success "PostgreSQL setup check completed!"
Write-Host ""
Write-Host "Next steps:" -ForegroundColor $InfoColor
Write-Host "1. Try running the main setup script again: .\setup-windows-clean.ps1"
Write-Host "2. Or manually continue with: cd backend && npm install && npx prisma migrate dev"
Write-Host ""
Write-Host "Database connection string:" -ForegroundColor $InfoColor
Write-Host "postgresql://tc_user:$DatabasePassword@localhost:5432/tc_platform"

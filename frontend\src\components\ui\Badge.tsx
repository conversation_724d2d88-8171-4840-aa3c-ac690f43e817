/**
 * Badge component
 * 
 * Reusable badge component for status indicators
 */

'use client';

import { cn } from '@/lib/utils';

interface BadgeProps {
  children: React.ReactNode;
  color?: 'gray' | 'red' | 'yellow' | 'green' | 'blue' | 'purple';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'solid' | 'outline';
  className?: string;
}

export function Badge({
  children,
  color = 'gray',
  size = 'md',
  variant = 'solid',
  className,
}: BadgeProps) {
  const baseClasses = 'inline-flex items-center font-medium rounded-full';
  
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-2.5 py-0.5 text-sm',
    lg: 'px-3 py-1 text-base',
  };

  const colorClasses = {
    solid: {
      gray: 'bg-gray-100 text-gray-800',
      red: 'bg-red-100 text-red-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      green: 'bg-green-100 text-green-800',
      blue: 'bg-blue-100 text-blue-800',
      purple: 'bg-purple-100 text-purple-800',
    },
    outline: {
      gray: 'border border-gray-300 text-gray-700',
      red: 'border border-red-300 text-red-700',
      yellow: 'border border-yellow-300 text-yellow-700',
      green: 'border border-green-300 text-green-700',
      blue: 'border border-blue-300 text-blue-700',
      purple: 'border border-purple-300 text-purple-700',
    },
  };

  return (
    <span
      className={cn(
        baseClasses,
        sizeClasses[size],
        colorClasses[variant][color],
        className
      )}
    >
      {children}
    </span>
  );
}

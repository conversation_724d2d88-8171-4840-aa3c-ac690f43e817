/**
 * MentionInput component
 * 
 * Text input with user mention support (@username)
 */

'use client';

import { useState, useRef, useEffect } from 'react';
import { MentionInputProps, UserMention, NoteValidation } from '@/types/note';
import { cn } from '@/lib/utils';

export function MentionInput({
  value,
  onChange,
  placeholder = 'Type your note...',
  disabled = false,
  error,
  availableUsers = [],
  maxLength = 2000,
}: MentionInputProps) {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<UserMention[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [mentionStart, setMentionStart] = useState(-1);
  const [mentionQuery, setMentionQuery] = useState('');
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Convert available users to UserMention format
  const userMentions: UserMention[] = availableUsers.map(user => ({
    ...user,
    displayName: `${user.firstName} ${user.lastName}`,
  }));

  useEffect(() => {
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const cursorPosition = e.target.selectionStart;
    
    // Check for mention trigger (@)
    const textBeforeCursor = newValue.substring(0, cursorPosition);
    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);
    
    if (mentionMatch) {
      const query = mentionMatch[1].toLowerCase();
      const start = cursorPosition - mentionMatch[0].length;
      
      setMentionStart(start);
      setMentionQuery(query);
      
      // Filter users based on query
      const filteredUsers = userMentions.filter(user =>
        user.firstName.toLowerCase().includes(query) ||
        user.lastName.toLowerCase().includes(query) ||
        user.displayName.toLowerCase().includes(query)
      );
      
      setSuggestions(filteredUsers);
      setShowSuggestions(filteredUsers.length > 0);
      setSelectedSuggestion(0);
    } else {
      setShowSuggestions(false);
      setMentionStart(-1);
      setMentionQuery('');
    }
    
    // Extract mentions and call onChange
    const mentions = NoteValidation.extractMentions(newValue);
    onChange(newValue, mentions);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestion(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestion(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      
      case 'Enter':
      case 'Tab':
        e.preventDefault();
        if (suggestions[selectedSuggestion]) {
          insertMention(suggestions[selectedSuggestion]);
        }
        break;
      
      case 'Escape':
        setShowSuggestions(false);
        break;
    }
  };

  const insertMention = (user: UserMention) => {
    if (!textareaRef.current || mentionStart === -1) return;

    const textarea = textareaRef.current;
    const cursorPosition = textarea.selectionStart;
    const textBefore = value.substring(0, mentionStart);
    const textAfter = value.substring(cursorPosition);
    
    const mentionMarkup = NoteValidation.createMentionMarkup(user.id, user.displayName);
    const newValue = textBefore + mentionMarkup + textAfter;
    const newCursorPosition = textBefore.length + mentionMarkup.length;
    
    // Extract mentions and call onChange
    const mentions = NoteValidation.extractMentions(newValue);
    onChange(newValue, mentions);
    
    // Reset mention state
    setShowSuggestions(false);
    setMentionStart(-1);
    setMentionQuery('');
    
    // Set cursor position after mention
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
      }
    }, 0);
  };

  const handleSuggestionClick = (user: UserMention) => {
    insertMention(user);
  };

  const getSuggestionPosition = () => {
    if (!textareaRef.current || mentionStart === -1) return { top: 0, left: 0 };

    // Simple positioning - in real app, would calculate exact cursor position
    const rect = textareaRef.current.getBoundingClientRect();
    return {
      top: rect.bottom + 4,
      left: rect.left,
    };
  };

  const characterCount = value.length;
  const isOverLimit = characterCount > maxLength;

  return (
    <div className="relative">
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        rows={3}
        className={cn(
          'w-full px-3 py-2 border rounded-md shadow-sm resize-none',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          'disabled:bg-gray-50 disabled:text-gray-500',
          error || isOverLimit
            ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
            : 'border-gray-300'
        )}
        style={{ minHeight: '80px', maxHeight: '200px' }}
      />

      {/* Character Count */}
      <div className="flex justify-between items-center mt-1">
        <div className="text-xs text-gray-500">
          Type @ to mention someone
        </div>
        <div className={cn(
          'text-xs',
          isOverLimit ? 'text-red-600' : 'text-gray-500'
        )}>
          {characterCount}/{maxLength}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {/* Mention Suggestions */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-64 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto"
          style={getSuggestionPosition()}
        >
          {suggestions.map((user, index) => (
            <div
              key={user.id}
              onClick={() => handleSuggestionClick(user)}
              className={cn(
                'px-3 py-2 cursor-pointer flex items-center space-x-2',
                index === selectedSuggestion
                  ? 'bg-blue-50 text-blue-900'
                  : 'hover:bg-gray-50'
              )}
            >
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-medium">
                  {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {user.displayName}
                </div>
                <div className="text-xs text-gray-500 truncate">
                  {user.email}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

/**
 * Contact-related types for the frontend
 */

import { PaginatedResponse } from './api';

/**
 * Contact role types
 */
export const ContactRoles = {
  BUYER: 'buyer',
  SELLER: 'seller',
  LISTING_AGENT: 'listing_agent',
  SELLING_AGENT: 'selling_agent',
  ATTORNEY: 'attorney',
  TITLE_COMPANY: 'title_company',
  LENDER: 'lender',
  INSPECTOR: 'inspector',
  APPRAISER: 'appraiser',
  CONTRACTOR: 'contractor',
  INSURANCE_AGENT: 'insurance_agent',
  ESCROW_OFFICER: 'escrow_officer',
  NOTARY: 'notary',
  OTHER: 'other',
} as const;

export type ContactRole = typeof ContactRoles[keyof typeof ContactRoles];

/**
 * Contact role display names
 */
export const ContactRoleDisplayNames: Record<ContactRole, string> = {
  [ContactRoles.BUYER]: 'Buyer',
  [ContactRoles.SELLER]: 'Seller',
  [ContactRoles.LISTING_AGENT]: 'Listing Agent',
  [ContactRoles.SELLING_AGENT]: 'Selling Agent',
  [ContactRoles.ATTORNEY]: 'Attorney',
  [ContactRoles.TITLE_COMPANY]: 'Title Company',
  [ContactRoles.LENDER]: 'Lender',
  [ContactRoles.INSPECTOR]: 'Inspector',
  [ContactRoles.APPRAISER]: 'Appraiser',
  [ContactRoles.CONTRACTOR]: 'Contractor',
  [ContactRoles.INSURANCE_AGENT]: 'Insurance Agent',
  [ContactRoles.ESCROW_OFFICER]: 'Escrow Officer',
  [ContactRoles.NOTARY]: 'Notary',
  [ContactRoles.OTHER]: 'Other',
};

/**
 * Contact interface
 */
export interface Contact {
  id: string;
  transactionId: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  role: ContactRole;
  company?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  
  // Related entities
  transaction: {
    id: string;
    propertyAddress: string;
    status: string;
  };
  
  // Calculated fields
  fullName: string;
  displayName: string;
}

/**
 * Contact summary (for lists)
 */
export interface ContactSummary {
  id: string;
  transactionId: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  role: ContactRole;
  company?: string;
  createdAt: string;
  
  // Related entities
  transaction: {
    id: string;
    propertyAddress: string;
  };
  
  // Calculated fields
  fullName: string;
  displayName: string;
}

/**
 * Contact creation data
 */
export interface CreateContactData {
  transactionId: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  role: ContactRole;
  company?: string;
  notes?: string;
}

/**
 * Contact update data
 */
export interface UpdateContactData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  role?: ContactRole;
  company?: string;
  notes?: string;
}

/**
 * Contact search criteria
 */
export interface ContactSearchCriteria {
  transactionId?: string;
  role?: ContactRole[];
  company?: string;
  search?: string;
  sortBy?: 'firstName' | 'lastName' | 'role' | 'company' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Contact list response
 */
export interface ContactListResponse extends PaginatedResponse<ContactSummary> {
  contacts: ContactSummary[];
}

/**
 * Contact statistics
 */
export interface ContactStats {
  total: number;
  byRole: Record<string, number>;
  byCompany: Record<string, number>;
  withEmail: number;
  withPhone: number;
  recentlyAdded: number;
}

/**
 * Bulk contact operation
 */
export interface BulkContactOperation {
  contactIds: string[];
  operation: 'delete' | 'update_role' | 'update_company' | 'export';
  data?: {
    role?: ContactRole;
    company?: string;
  };
}

/**
 * Contact form validation
 */
export interface ContactFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  role?: string;
  company?: string;
  notes?: string;
  general?: string;
}

/**
 * Contact modal props
 */
export interface ContactModalProps {
  isOpen: boolean;
  onClose: () => void;
  contact?: Contact;
  transactionId?: string;
  mode: 'create' | 'edit' | 'view';
  onSave?: (contact: Contact) => void;
}

/**
 * Contact list props
 */
export interface ContactListProps {
  transactionId?: string;
  searchCriteria?: ContactSearchCriteria;
  onContactSelect?: (contact: Contact) => void;
  onContactEdit?: (contact: Contact) => void;
  onContactDelete?: (contactId: string) => void;
  showActions?: boolean;
  compact?: boolean;
}

/**
 * Contact card props
 */
export interface ContactCardProps {
  contact: ContactSummary;
  onEdit?: (contact: ContactSummary) => void;
  onDelete?: (contactId: string) => void;
  onView?: (contact: ContactSummary) => void;
  showActions?: boolean;
  compact?: boolean;
}

/**
 * Contact form props
 */
export interface ContactFormProps {
  contact?: Contact;
  transactionId?: string;
  onSubmit: (data: CreateContactData | UpdateContactData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  errors?: ContactFormErrors;
}

/**
 * Contact filter props
 */
export interface ContactFilterProps {
  criteria: ContactSearchCriteria;
  onCriteriaChange: (criteria: ContactSearchCriteria) => void;
  onReset: () => void;
  availableRoles?: ContactRole[];
  availableCompanies?: string[];
}

/**
 * Contact export data
 */
export interface ContactExportData {
  transactionId: string;
  propertyAddress: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email?: string;
  phone?: string;
  role: string;
  roleDisplayName: string;
  company?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

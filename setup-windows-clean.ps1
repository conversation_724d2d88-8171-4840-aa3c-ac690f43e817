# TC Platform Windows Setup Script (Clean Version)
# Run this script as Administrator in PowerShell

param(
    [string]$DatabasePassword = "tc_password_2024",
    [string]$JwtSecret = "",
    [switch]$SkipPrerequisites = $false,
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "TC Platform Windows Setup Script"
    Write-Host ""
    Write-Host "Usage: .\setup-windows-clean.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -DatabasePassword    Set custom database password"
    Write-Host "  -JwtSecret          Set custom JWT secret"
    Write-Host "  -SkipPrerequisites  Skip installation of Node.js and PostgreSQL"
    Write-Host "  -Help               Show this help message"
    exit 0
}

# Colors for output
$ErrorColor = "Red"
$SuccessColor = "Green"
$InfoColor = "Cyan"
$WarningColor = "Yellow"

function Write-Step {
    param([string]$Message)
    Write-Host ""
    Write-Host "🔄 $Message" -ForegroundColor $InfoColor
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor $SuccessColor
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor $ErrorColor
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor $WarningColor
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-Chocolatey {
    Write-Step "Installing Chocolatey package manager..."
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Success "Chocolatey installed successfully"
        return $true
    }
    catch {
        Write-Error "Failed to install Chocolatey: $($_.Exception.Message)"
        return $false
    }
}

function Install-Prerequisites {
    Write-Step "Installing prerequisites..."

    # Check if Chocolatey is installed
    if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
        if (!(Install-Chocolatey)) {
            return $false
        }
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    }

    # Install Node.js
    Write-Step "Installing Node.js..."
    try {
        choco install nodejs -y
        Write-Success "Node.js installed"
    }
    catch {
        Write-Error "Failed to install Node.js: $($_.Exception.Message)"
        return $false
    }

    # Install PostgreSQL
    Write-Step "Installing PostgreSQL..."
    try {
        choco install postgresql --params "/Password:$DatabasePassword" -y
        Write-Success "PostgreSQL installed"
    }
    catch {
        Write-Error "Failed to install PostgreSQL: $($_.Exception.Message)"
        return $false
    }

    Write-Success "All prerequisites installed successfully"
    return $true
}

function Get-PostgreSQLPath {
    # Try to find PostgreSQL installation
    $possiblePaths = @(
        "C:\Program Files\PostgreSQL\17\bin",
        "C:\Program Files\PostgreSQL\16\bin",
        "C:\Program Files\PostgreSQL\15\bin",
        "C:\Program Files\PostgreSQL\14\bin",
        "C:\Program Files\PostgreSQL\13\bin"
    )

    foreach ($path in $possiblePaths) {
        if (Test-Path "$path\pg_isready.exe") {
            return $path
        }
    }

    # Try to find via PATH
    $pgReady = Get-Command pg_isready -ErrorAction SilentlyContinue
    if ($pgReady) {
        return Split-Path $pgReady.Source
    }

    return $null
}

function Wait-ForPostgreSQL {
    Write-Step "Waiting for PostgreSQL to start..."
    $maxAttempts = 30
    $attempt = 0

    $pgPath = Get-PostgreSQLPath
    if (!$pgPath) {
        Write-Error "Could not find PostgreSQL installation. Please ensure PostgreSQL is installed."
        return $false
    }

    Write-Host "Using PostgreSQL at: $pgPath" -ForegroundColor $InfoColor

    do {
        $attempt++
        try {
            $null = & "$pgPath\pg_isready.exe" -h localhost -p 5432 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Success "PostgreSQL is ready"
                return $true
            }
        }
        catch {
            # Continue trying
        }

        if ($attempt -lt $maxAttempts) {
            Write-Host "Attempt $attempt/$maxAttempts - PostgreSQL not ready yet, waiting 2 seconds..." -ForegroundColor $WarningColor
            Start-Sleep -Seconds 2
        }
    } while ($attempt -lt $maxAttempts)

    Write-Error "PostgreSQL failed to start after $maxAttempts attempts"
    return $false
}

function Setup-Database {
    Write-Step "Setting up database..."

    # Try to start PostgreSQL service (try different possible service names)
    $serviceNames = @("postgresql-x64-17", "postgresql-x64-16", "postgresql-x64-15", "postgresql")
    $serviceStarted = $false

    foreach ($serviceName in $serviceNames) {
        try {
            $service = Get-Service $serviceName -ErrorAction SilentlyContinue
            if ($service) {
                Start-Service $serviceName -ErrorAction SilentlyContinue
                Write-Success "PostgreSQL service '$serviceName' started"
                $serviceStarted = $true
                break
            }
        }
        catch {
            # Continue to next service name
        }
    }

    if (!$serviceStarted) {
        Write-Warning "Could not start PostgreSQL service automatically. Please start it manually."
    }

    # Wait for PostgreSQL to be ready
    if (!(Wait-ForPostgreSQL)) {
        return $false
    }

    # Get PostgreSQL path
    $pgPath = Get-PostgreSQLPath
    if (!$pgPath) {
        Write-Error "Could not find PostgreSQL installation."
        return $false
    }

    # Create database and user
    $sqlCommands = @"
CREATE DATABASE tc_platform;
CREATE USER tc_user WITH PASSWORD '$DatabasePassword';
GRANT ALL PRIVILEGES ON DATABASE tc_platform TO tc_user;
ALTER USER tc_user CREATEDB;
"@

    try {
        $sqlCommands | & "$pgPath\psql.exe" -U postgres -h localhost
        Write-Success "Database and user created successfully"
        return $true
    }
    catch {
        Write-Error "Failed to create database: $($_.Exception.Message)"
        return $false
    }
}

function Install-Dependencies {
    Write-Step "Installing project dependencies..."

    # Backend dependencies
    Write-Step "Installing backend dependencies..."
    try {
        Set-Location "backend"
        npm install
        Write-Success "Backend dependencies installed"
        Set-Location ".."
    }
    catch {
        Write-Error "Failed to install backend dependencies: $($_.Exception.Message)"
        return $false
    }

    # Frontend dependencies
    Write-Step "Installing frontend dependencies..."
    try {
        Set-Location "frontend"
        npm install
        Write-Success "Frontend dependencies installed"
        Set-Location ".."
    }
    catch {
        Write-Error "Failed to install frontend dependencies: $($_.Exception.Message)"
        return $false
    }

    return $true
}

function Generate-JwtSecret {
    if ([string]::IsNullOrEmpty($JwtSecret)) {
        $bytes = New-Object byte[] 32
        [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
        return [Convert]::ToBase64String($bytes)
    }
    return $JwtSecret
}

function Create-EnvironmentFiles {
    Write-Step "Creating environment files..."

    $generatedJwtSecret = Generate-JwtSecret

    # Backend .env
    $backendEnv = @"
# Database
DATABASE_URL="postgresql://tc_user:$DatabasePassword@localhost:5432/tc_platform"

# JWT
JWT_SECRET="$generatedJwtSecret"
JWT_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV=development

# CORS
FRONTEND_URL="http://localhost:3000"
"@

    try {
        $backendEnv | Out-File -FilePath "backend\.env" -Encoding UTF8
        Write-Success "Backend .env file created"
    }
    catch {
        Write-Error "Failed to create backend .env file: $($_.Exception.Message)"
        return $false
    }

    # Frontend .env.local
    $frontendEnv = @"
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Environment
NODE_ENV=development
"@

    try {
        $frontendEnv | Out-File -FilePath "frontend\.env.local" -Encoding UTF8
        Write-Success "Frontend .env.local file created"
    }
    catch {
        Write-Error "Failed to create frontend .env.local file: $($_.Exception.Message)"
        return $false
    }

    return $true
}

function Setup-Database-Schema {
    Write-Step "Setting up database schema..."

    try {
        Set-Location "backend"

        # Generate Prisma client
        Write-Step "Generating Prisma client..."
        npx prisma generate
        Write-Success "Prisma client generated"

        # Run migrations
        Write-Step "Running database migrations..."
        npx prisma migrate dev --name init
        Write-Success "Database migrations completed"

        # Seed database
        Write-Step "Seeding database with sample data..."
        npx prisma db seed
        Write-Success "Database seeded with sample data"

        Set-Location ".."
        return $true
    }
    catch {
        Write-Error "Failed to setup database schema: $($_.Exception.Message)"
        Set-Location ".."
        return $false
    }
}

function Create-StartupScripts {
    Write-Step "Creating startup scripts..."

    # Create start-both.bat
    $startBoth = @"
@echo off
echo Starting TC Platform (Backend and Frontend)...
echo.
echo Starting Backend...
start "TC Platform Backend" cmd /k "cd backend && npm run dev"
timeout /t 5 /nobreak > nul
echo Starting Frontend...
start "TC Platform Frontend" cmd /k "cd frontend && npm run dev"
echo.
echo Both services are starting...
echo Backend: http://localhost:3001
echo Frontend: http://localhost:3000
echo.
pause
"@

    try {
        $startBoth | Out-File -FilePath "start-both.bat" -Encoding ASCII
        Write-Success "start-both.bat created"
    }
    catch {
        Write-Error "Failed to create start-both.bat: $($_.Exception.Message)"
    }
}

# Main execution
try {
    Write-Host "🚀 TC Platform Windows Setup Script" -ForegroundColor $InfoColor
    Write-Host "=====================================" -ForegroundColor $InfoColor

    # Check if running as administrator
    if (!(Test-Administrator)) {
        Write-Error "This script must be run as Administrator. Please restart PowerShell as Administrator and try again."
        exit 1
    }

    # Check if we're in the right directory
    if (!(Test-Path "backend") -or !(Test-Path "frontend")) {
        Write-Error "Please run this script from the TC Platform root directory (where backend and frontend folders are located)."
        exit 1
    }

    # Install prerequisites
    if (!$SkipPrerequisites) {
        if (!(Install-Prerequisites)) {
            Write-Error "Failed to install prerequisites. Please install Node.js and PostgreSQL manually."
            exit 1
        }
    }

    # Setup database
    if (!(Setup-Database)) {
        Write-Error "Database setup failed. Please check PostgreSQL installation."
        exit 1
    }

    # Install dependencies
    if (!(Install-Dependencies)) {
        Write-Error "Failed to install project dependencies."
        exit 1
    }

    # Create environment files
    if (!(Create-EnvironmentFiles)) {
        Write-Error "Failed to create environment files."
        exit 1
    }

    # Setup database schema
    if (!(Setup-Database-Schema)) {
        Write-Error "Failed to setup database schema."
        exit 1
    }

    # Create startup scripts
    Create-StartupScripts

    Write-Host ""
    Write-Host "🎉 Setup completed successfully!" -ForegroundColor $SuccessColor
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor $InfoColor
    Write-Host "1. Double-click 'start-both.bat' to start the application"
    Write-Host "2. Open http://localhost:3000 in your browser"
    Write-Host "3. Explore the Contact and Communication features"
    Write-Host ""
}
catch {
    Write-Error "Setup failed: $($_.Exception.Message)"
    Write-Host "Please check the error messages above and try again." -ForegroundColor $WarningColor
    exit 1
}

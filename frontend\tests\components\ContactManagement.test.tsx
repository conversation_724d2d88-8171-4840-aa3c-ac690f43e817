/**
 * ContactManagement Component Tests
 * 
 * Comprehensive tests for the ContactManagement component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ContactManagement } from '@/components/contacts/ContactManagement';

// Mock the hooks
jest.mock('@/hooks', () => ({
  useContacts: jest.fn(),
  useCreateContact: jest.fn(),
  useUpdateContact: jest.fn(),
  useDeleteContact: jest.fn(),
  useContactStats: jest.fn(),
}));

// Mock the API client
jest.mock('@/lib/api', () => ({
  contactApi: {
    getContacts: jest.fn(),
    createContact: jest.fn(),
    updateContact: jest.fn(),
    deleteContact: jest.fn(),
  },
}));

const mockContacts = [
  {
    id: 'contact-1',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '555-0123',
    role: 'buyer',
    company: 'Test Company',
    transactionId: 'trans-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'contact-2',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '555-0456',
    role: 'seller',
    company: 'Another Company',
    transactionId: 'trans-1',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('ContactManagement Component', () => {
  const mockUseContacts = require('@/hooks').useContacts;
  const mockUseCreateContact = require('@/hooks').useCreateContact;
  const mockUseUpdateContact = require('@/hooks').useUpdateContact;
  const mockUseDeleteContact = require('@/hooks').useDeleteContact;
  const mockUseContactStats = require('@/hooks').useContactStats;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup default mock implementations
    mockUseContacts.mockReturnValue({
      data: { items: mockContacts, total: 2, page: 1, totalPages: 1 },
      isLoading: false,
      error: null,
    });

    mockUseCreateContact.mockReturnValue({
      mutateAsync: jest.fn().mockResolvedValue(mockContacts[0]),
      isLoading: false,
    });

    mockUseUpdateContact.mockReturnValue({
      mutateAsync: jest.fn().mockResolvedValue(mockContacts[0]),
      isLoading: false,
    });

    mockUseDeleteContact.mockReturnValue({
      mutateAsync: jest.fn().mockResolvedValue(undefined),
      isLoading: false,
    });

    mockUseContactStats.mockReturnValue({
      data: { total: 2, byRole: { buyer: 1, seller: 1 } },
      isLoading: false,
    });
  });

  describe('Rendering', () => {
    test('renders contact management interface', () => {
      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText('Test Contacts')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    test('renders compact view when compact prop is true', () => {
      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={false}
          compact={true}
        />,
        { wrapper: createWrapper() }
      );

      // In compact view, should show fewer details
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('Test Company')).not.toBeInTheDocument();
    });

    test('shows add button when showAddButton is true', () => {
      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText('Add Contact')).toBeInTheDocument();
    });

    test('hides add button when showAddButton is false', () => {
      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={false}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.queryByText('Add Contact')).not.toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    test('shows loading spinner when data is loading', () => {
      mockUseContacts.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      });

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText('Loading contacts...')).toBeInTheDocument();
    });
  });

  describe('Error States', () => {
    test('shows error message when there is an error', () => {
      mockUseContacts.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Failed to load contacts'),
      });

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText(/error loading contacts/i)).toBeInTheDocument();
    });
  });

  describe('Search and Filter', () => {
    test('renders search input', () => {
      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByPlaceholderText(/search contacts/i)).toBeInTheDocument();
    });

    test('calls search function when typing in search input', async () => {
      const user = userEvent.setup();

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      await user.type(searchInput, 'John');

      await waitFor(() => {
        expect(mockUseContacts).toHaveBeenCalledWith(
          expect.objectContaining({
            search: 'John',
          })
        );
      });
    });

    test('renders role filter dropdown', () => {
      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByDisplayValue('All Roles')).toBeInTheDocument();
    });
  });

  describe('Contact Actions', () => {
    test('opens add contact modal when add button is clicked', async () => {
      const user = userEvent.setup();

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      const addButton = screen.getByText('Add Contact');
      await user.click(addButton);

      expect(screen.getByText('Add New Contact')).toBeInTheDocument();
    });

    test('opens edit modal when edit button is clicked', async () => {
      const user = userEvent.setup();

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      const editButtons = screen.getAllByText('Edit');
      await user.click(editButtons[0]);

      expect(screen.getByText('Edit Contact')).toBeInTheDocument();
    });

    test('shows delete confirmation when delete button is clicked', async () => {
      const user = userEvent.setup();

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      const deleteButtons = screen.getAllByText('Delete');
      await user.click(deleteButtons[0]);

      expect(screen.getByText(/are you sure you want to delete/i)).toBeInTheDocument();
    });
  });

  describe('Contact Creation', () => {
    test('creates new contact when form is submitted', async () => {
      const user = userEvent.setup();
      const mockCreate = jest.fn().mockResolvedValue(mockContacts[0]);
      mockUseCreateContact.mockReturnValue({
        mutateAsync: mockCreate,
        isLoading: false,
      });

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      // Open add modal
      await user.click(screen.getByText('Add Contact'));

      // Fill form
      await user.type(screen.getByLabelText(/first name/i), 'New');
      await user.type(screen.getByLabelText(/last name/i), 'Contact');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.selectOptions(screen.getByLabelText(/role/i), 'buyer');

      // Submit form
      await user.click(screen.getByText('Save Contact'));

      await waitFor(() => {
        expect(mockCreate).toHaveBeenCalledWith(
          expect.objectContaining({
            firstName: 'New',
            lastName: 'Contact',
            email: '<EMAIL>',
            role: 'buyer',
            transactionId: 'trans-1',
          })
        );
      });
    });
  });

  describe('Contact Updates', () => {
    test('updates contact when edit form is submitted', async () => {
      const user = userEvent.setup();
      const mockUpdate = jest.fn().mockResolvedValue(mockContacts[0]);
      mockUseUpdateContact.mockReturnValue({
        mutateAsync: mockUpdate,
        isLoading: false,
      });

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      // Open edit modal
      const editButtons = screen.getAllByText('Edit');
      await user.click(editButtons[0]);

      // Update form
      const firstNameInput = screen.getByDisplayValue('John');
      await user.clear(firstNameInput);
      await user.type(firstNameInput, 'Updated');

      // Submit form
      await user.click(screen.getByText('Save Contact'));

      await waitFor(() => {
        expect(mockUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            id: 'contact-1',
            firstName: 'Updated',
          })
        );
      });
    });
  });

  describe('Contact Deletion', () => {
    test('deletes contact when deletion is confirmed', async () => {
      const user = userEvent.setup();
      const mockDelete = jest.fn().mockResolvedValue(undefined);
      mockUseDeleteContact.mockReturnValue({
        mutateAsync: mockDelete,
        isLoading: false,
      });

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      // Click delete button
      const deleteButtons = screen.getAllByText('Delete');
      await user.click(deleteButtons[0]);

      // Confirm deletion
      await user.click(screen.getByText('Delete Contact'));

      await waitFor(() => {
        expect(mockDelete).toHaveBeenCalledWith('contact-1');
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels', () => {
      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByRole('button', { name: /add contact/i })).toBeInTheDocument();
      expect(screen.getByRole('textbox', { name: /search contacts/i })).toBeInTheDocument();
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <ContactManagement
          transactionId="trans-1"
          title="Test Contacts"
          showAddButton={true}
          compact={false}
        />,
        { wrapper: createWrapper() }
      );

      // Tab to add button and press Enter
      await user.tab();
      await user.keyboard('{Enter}');

      expect(screen.getByText('Add New Contact')).toBeInTheDocument();
    });
  });
});

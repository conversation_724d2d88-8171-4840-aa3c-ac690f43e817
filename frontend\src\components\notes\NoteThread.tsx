/**
 * NoteThread component
 * 
 * Displays notes in a conversation thread format
 */

'use client';

import { useState, useEffect, useRef } from 'react';
import { NoteThreadProps, NoteSummary, NoteValidation, CreateNoteData } from '@/types/note';
import { NoteForm } from './NoteForm';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { cn } from '@/lib/utils';

// Icons
const EditIcon = () => <span>✏️</span>;
const DeleteIcon = () => <span>🗑️</span>;

export function NoteThread({
  transactionId,
  onNoteAdd,
  onNoteEdit,
  onNoteDelete,
  currentUserId = 'user-1',
  compact = false,
}: NoteThreadProps) {
  const [notes, setNotes] = useState<NoteSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  
  const threadEndRef = useRef<HTMLDivElement>(null);

  // Mock available users for mentions
  const availableUsers = [
    {
      id: 'user-1',
      firstName: 'Jane',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
    {
      id: 'user-2',
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
    },
    {
      id: 'user-3',
      firstName: 'Bob',
      lastName: 'Johnson',
      email: '<EMAIL>',
    },
  ];

  // Mock notes data
  const mockNotes: NoteSummary[] = [
    {
      id: '1',
      transactionId,
      userId: 'user-1',
      content: 'Just spoke with the buyer. They are excited about the property and want to schedule an inspection for next week. @[John Smith](user-2) can you coordinate with the inspector?',
      mentions: ['user-2'],
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      transaction: {
        id: transactionId,
        propertyAddress: '123 Main St, Anytown, PA 12345',
      },
      user: {
        id: 'user-1',
        firstName: 'Jane',
        lastName: 'Doe',
      },
      isEdited: false,
      timeAgo: '',
      mentionCount: 1,
      contentPreview: '',
    },
    {
      id: '2',
      transactionId,
      userId: 'user-2',
      content: 'Absolutely! I\'ll reach out to Tom Wilson from ABC Inspections. He\'s available Thursday at 2 PM. Does that work for everyone?',
      mentions: [],
      createdAt: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
      transaction: {
        id: transactionId,
        propertyAddress: '123 Main St, Anytown, PA 12345',
      },
      user: {
        id: 'user-2',
        firstName: 'John',
        lastName: 'Smith',
      },
      isEdited: false,
      timeAgo: '',
      mentionCount: 0,
      contentPreview: '',
    },
    {
      id: '3',
      transactionId,
      userId: 'user-1',
      content: 'Perfect! Thursday at 2 PM works great. I\'ll let the buyer know.',
      mentions: [],
      createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      transaction: {
        id: transactionId,
        propertyAddress: '123 Main St, Anytown, PA 12345',
      },
      user: {
        id: 'user-1',
        firstName: 'Jane',
        lastName: 'Doe',
      },
      isEdited: false,
      timeAgo: '',
      mentionCount: 0,
      contentPreview: '',
    },
  ];

  useEffect(() => {
    loadNotes();
  }, [transactionId]);

  useEffect(() => {
    // Scroll to bottom when new notes are added
    scrollToBottom();
  }, [notes]);

  const loadNotes = async () => {
    setLoading(true);
    setError(null);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const processedNotes = mockNotes.map(note => ({
        ...note,
        timeAgo: NoteValidation.getTimeAgo(note.createdAt),
        isEdited: NoteValidation.isEdited(note.createdAt, note.updatedAt),
        contentPreview: NoteValidation.getContentPreview(note.content),
      }));
      
      setNotes(processedNotes);
    } catch (error) {
      setError('Failed to load notes');
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    threadEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleAddNote = async (data: CreateNoteData) => {
    setSubmitting(true);
    
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newNote: NoteSummary = {
        id: Date.now().toString(),
        transactionId,
        userId: currentUserId,
        content: data.content,
        mentions: data.mentions || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        transaction: {
          id: transactionId,
          propertyAddress: '123 Main St, Anytown, PA 12345',
        },
        user: {
          id: currentUserId,
          firstName: 'Current',
          lastName: 'User',
        },
        isEdited: false,
        timeAgo: 'just now',
        mentionCount: data.mentions?.length || 0,
        contentPreview: NoteValidation.getContentPreview(data.content),
      };
      
      setNotes(prev => [...prev, newNote]);
      setShowAddForm(false);
      
      if (onNoteAdd) {
        onNoteAdd(newNote as any);
      }
    } catch (error) {
      console.error('Error adding note:', error);
      throw error;
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditNote = async (noteId: string, data: any) => {
    setSubmitting(true);
    
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setNotes(prev => prev.map(note => 
        note.id === noteId 
          ? {
              ...note,
              content: data.content,
              mentions: data.mentions || [],
              updatedAt: new Date().toISOString(),
              isEdited: true,
              mentionCount: data.mentions?.length || 0,
              contentPreview: NoteValidation.getContentPreview(data.content),
            }
          : note
      ));
      
      setEditingNoteId(null);
      
      if (onNoteEdit) {
        const updatedNote = notes.find(n => n.id === noteId);
        if (updatedNote) {
          onNoteEdit({ ...updatedNote, ...data } as any);
        }
      }
    } catch (error) {
      console.error('Error editing note:', error);
      throw error;
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    if (!window.confirm('Are you sure you want to delete this note?')) {
      return;
    }

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setNotes(prev => prev.filter(note => note.id !== noteId));
      
      if (onNoteDelete) {
        onNoteDelete(noteId);
      }
    } catch (error) {
      console.error('Error deleting note:', error);
    }
  };

  const formatContent = (content: string) => {
    // Simple mention formatting
    return content.replace(/@\[([^\]]+)\]\(([^)]+)\)/g, (match, name, userId) => {
      return `<span class="text-blue-600 font-medium">@${name}</span>`;
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">Loading conversation...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600">{error}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={loadNotes}
          className="mt-2"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Thread Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Conversation ({notes.length})
        </h3>
        <Button
          onClick={() => setShowAddForm(true)}
          size="sm"
          disabled={showAddForm}
        >
          Add Note
        </Button>
      </div>

      {/* Notes Thread */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {notes.map((note, index) => {
          const isCurrentUser = note.userId === currentUserId;
          const isEditing = editingNoteId === note.id;
          
          return (
            <div
              key={note.id}
              className={cn(
                'flex',
                isCurrentUser ? 'justify-end' : 'justify-start'
              )}
            >
              <div
                className={cn(
                  'max-w-xs lg:max-w-md px-4 py-3 rounded-lg',
                  isCurrentUser
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-900'
                )}
              >
                {/* User Info */}
                <div className="flex items-center justify-between mb-2">
                  <span className={cn(
                    'text-sm font-medium',
                    isCurrentUser ? 'text-blue-100' : 'text-gray-600'
                  )}>
                    {isCurrentUser ? 'You' : `${note.user.firstName} ${note.user.lastName}`}
                  </span>
                  
                  <div className="flex items-center space-x-1">
                    <span className={cn(
                      'text-xs',
                      isCurrentUser ? 'text-blue-200' : 'text-gray-500'
                    )}>
                      {note.timeAgo}
                    </span>
                    
                    {isCurrentUser && (
                      <div className="flex items-center space-x-1 ml-2">
                        <button
                          onClick={() => setEditingNoteId(note.id)}
                          className="text-blue-200 hover:text-white"
                          title="Edit note"
                        >
                          <EditIcon />
                        </button>
                        <button
                          onClick={() => handleDeleteNote(note.id)}
                          className="text-blue-200 hover:text-white"
                          title="Delete note"
                        >
                          <DeleteIcon />
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Content */}
                {isEditing ? (
                  <div className="bg-white rounded p-2">
                    <NoteForm
                      note={note as any}
                      onSubmit={(data) => handleEditNote(note.id, data)}
                      onCancel={() => setEditingNoteId(null)}
                      loading={submitting}
                      placeholder="Edit your note..."
                      showMentions={true}
                      availableUsers={availableUsers}
                    />
                  </div>
                ) : (
                  <div
                    className="text-sm whitespace-pre-wrap break-words"
                    dangerouslySetInnerHTML={{
                      __html: formatContent(note.content)
                    }}
                  />
                )}

                {note.isEdited && !isEditing && (
                  <div className={cn(
                    'text-xs mt-1',
                    isCurrentUser ? 'text-blue-200' : 'text-gray-500'
                  )}>
                    (edited)
                  </div>
                )}
              </div>
            </div>
          );
        })}
        
        <div ref={threadEndRef} />
      </div>

      {/* Add Note Form */}
      {showAddForm && (
        <div className="border-t border-gray-200 pt-4">
          <NoteForm
            transactionId={transactionId}
            onSubmit={handleAddNote}
            onCancel={() => setShowAddForm(false)}
            loading={submitting}
            placeholder="Type your message..."
            showMentions={true}
            availableUsers={availableUsers}
          />
        </div>
      )}
    </div>
  );
}

/**
 * Quick actions component
 */

import Link from 'next/link';
import {
  PlusIcon,
  DocumentPlusIcon,
  UserPlusIcon,
  ClipboardDocumentListIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';

const quickActions = [
  {
    name: 'New Transaction',
    description: 'Create a new real estate transaction',
    href: '/dashboard/transactions/new',
    icon: PlusIcon,
    color: 'bg-primary-600 hover:bg-primary-700',
  },
  {
    name: 'Upload Document',
    description: 'Add documents to existing transactions',
    href: '/dashboard/documents/upload',
    icon: DocumentPlusIcon,
    color: 'bg-green-600 hover:bg-green-700',
  },
  {
    name: 'Add Contact',
    description: 'Add a new contact to your database',
    href: '/dashboard/contacts/new',
    icon: UserPlusIcon,
    color: 'bg-blue-600 hover:bg-blue-700',
  },
  {
    name: 'Create Task',
    description: 'Add a custom task or reminder',
    href: '/dashboard/tasks/new',
    icon: ClipboardDocumentListIcon,
    color: 'bg-purple-600 hover:bg-purple-700',
  },
];

export function QuickActions() {
  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Quick Actions
        </h3>

        <div className="space-y-3">
          {quickActions.map((action) => (
            <Link key={action.name} href={action.href}>
              <div className="group relative bg-white p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all cursor-pointer">
                <div className="flex items-start space-x-3">
                  <div className={`flex-shrink-0 p-2 rounded-md ${action.color}`}>
                    <action.icon className="h-5 w-5 text-white" aria-hidden="true" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 group-hover:text-primary-600">
                      {action.name}
                    </h4>
                    <p className="text-sm text-gray-500 mt-1">
                      {action.description}
                    </p>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Quick Links
          </h4>
          <div className="space-y-2">
            <a
              href="/dashboard/transactions"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              📊 View All Transactions
            </a>
            <a
              href="/demo/contact-communication"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              🚀 Contact & Communication Demo
            </a>
            <a
              href="/help"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              📚 View Documentation
            </a>
            <a
              href="/support"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              💬 Contact Support
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * NoteCard component
 * 
 * Displays a note in a card format with actions and mention support
 */

'use client';

import { useState } from 'react';
import { NoteCardProps, NoteValidation } from '@/types/note';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';

// Icons (using simple text for now, can be replaced with icon library)
const EditIcon = () => <span>✏️</span>;
const DeleteIcon = () => <span>🗑️</span>;
const ReplyIcon = () => <span>💬</span>;
const MentionIcon = () => <span>@</span>;

export function NoteCard({
  note,
  onEdit,
  onDelete,
  onView,
  onReply,
  showActions = true,
  compact = false,
  currentUserId,
}: NoteCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showFullContent, setShowFullContent] = useState(false);

  const isAuthor = currentUserId === note.userId;
  const canEdit = isAuthor;
  const canDelete = isAuthor; // In real app, also check for admin/TC roles

  const handleDelete = async () => {
    if (!onDelete) return;
    
    if (window.confirm('Are you sure you want to delete this note?')) {
      setIsDeleting(true);
      try {
        await onDelete(note.id);
      } catch (error) {
        console.error('Error deleting note:', error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(note);
    }
  };

  const handleView = () => {
    if (onView) {
      onView(note);
    }
  };

  const handleReply = () => {
    if (onReply) {
      onReply(note);
    }
  };

  const formatContent = (content: string) => {
    // Simple mention formatting - in real app, would use proper mention parsing
    return content.replace(/@\[([^\]]+)\]\(([^)]+)\)/g, (match, name, userId) => {
      return `<span class="text-blue-600 font-medium">@${name}</span>`;
    });
  };

  const displayContent = showFullContent ? note.content : note.contentPreview;
  const needsTruncation = note.content.length > note.contentPreview.length;

  return (
    <div
      className={cn(
        'bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow',
        compact ? 'p-3' : 'p-4',
        'group'
      )}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          {/* Avatar */}
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {note.user.firstName.charAt(0)}{note.user.lastName.charAt(0)}
              </span>
            </div>
          </div>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h4 className={cn(
                'font-medium text-gray-900',
                compact ? 'text-sm' : 'text-base'
              )}>
                {note.user.firstName} {note.user.lastName}
              </h4>
              {isAuthor && (
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  You
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <span className="text-xs text-gray-500">
                {note.timeAgo}
              </span>
              {note.isEdited && (
                <span className="text-xs text-gray-400">(edited)</span>
              )}
              {note.mentionCount > 0 && (
                <div className="flex items-center text-xs text-blue-600">
                  <MentionIcon />
                  <span className="ml-1">{note.mentionCount}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {onReply && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleReply}
                className="p-1 h-8 w-8"
                title="Reply to note"
              >
                <ReplyIcon />
              </Button>
            )}
            {canEdit && onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="p-1 h-8 w-8"
                title="Edit note"
              >
                <EditIcon />
              </Button>
            )}
            {canDelete && onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                loading={isDeleting}
                className="p-1 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                title="Delete note"
              >
                <DeleteIcon />
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="mb-3">
        <div
          className={cn(
            'text-gray-900 whitespace-pre-wrap break-words',
            compact ? 'text-sm' : 'text-base'
          )}
          dangerouslySetInnerHTML={{
            __html: formatContent(displayContent)
          }}
        />
        
        {needsTruncation && (
          <button
            onClick={() => setShowFullContent(!showFullContent)}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium mt-2"
          >
            {showFullContent ? 'Show less' : 'Show more'}
          </button>
        )}
      </div>

      {/* Transaction Info */}
      {!compact && (
        <div className="pt-3 border-t border-gray-100">
          <div className="text-xs text-gray-500">
            <span className="font-medium">Transaction:</span>
            <span className="ml-1 truncate" title={note.transaction.propertyAddress}>
              {note.transaction.propertyAddress}
            </span>
          </div>
        </div>
      )}

      {/* Click handler for view */}
      {onView && (
        <div
          className="absolute inset-0 cursor-pointer"
          onClick={handleView}
          style={{ zIndex: 1 }}
        />
      )}
    </div>
  );
}

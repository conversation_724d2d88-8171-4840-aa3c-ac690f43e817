/**
 * Note API Integration Tests
 * 
 * Comprehensive tests for all note API endpoints including mentions
 */

const request = require('supertest');
const app = require('../../src/app');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

describe('Note API Integration Tests', () => {
  let authToken;
  let testBrokerageId;
  let testTransactionId;
  let testUserId;
  let testNoteId;
  let mentionedUserId;

  beforeAll(async () => {
    // Setup test data
    const testBrokerage = await prisma.brokerage.create({
      data: {
        name: 'Test Brokerage',
        address: '123 Test St',
        phone: '555-0123',
        email: '<EMAIL>',
      },
    });
    testBrokerageId = testBrokerage.id;

    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'TC',
        brokerageId: testBrokerageId,
      },
    });
    testUserId = testUser.id;

    const mentionedUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Mentioned',
        lastName: 'User',
        role: 'AGENT',
        brokerageId: testBrokerageId,
      },
    });
    mentionedUserId = mentionedUser.id;

    const testTransaction = await prisma.transaction.create({
      data: {
        propertyAddress: '123 Test Property St',
        transactionType: 'PURCHASE',
        status: 'ACTIVE',
        brokerageId: testBrokerageId,
        tcId: testUserId,
      },
    });
    testTransactionId = testTransaction.id;

    authToken = 'mock-jwt-token';
  });

  afterAll(async () => {
    // Cleanup test data
    await prisma.noteMention.deleteMany({});
    await prisma.note.deleteMany({ where: { transactionId: testTransactionId } });
    await prisma.transaction.deleteMany({ where: { id: testTransactionId } });
    await prisma.user.deleteMany({ where: { brokerageId: testBrokerageId } });
    await prisma.brokerage.deleteMany({ where: { id: testBrokerageId } });
    await prisma.$disconnect();
  });

  describe('POST /api/notes', () => {
    test('should create a new note', async () => {
      const noteData = {
        transactionId: testTransactionId,
        content: 'This is a test note',
        mentions: [],
      };

      const response = await request(app)
        .post('/api/notes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(noteData)
        .expect(201);

      expect(response.body).toMatchObject({
        content: 'This is a test note',
        transactionId: testTransactionId,
        userId: testUserId,
      });

      testNoteId = response.body.id;
    });

    test('should create note with mentions', async () => {
      const noteData = {
        transactionId: testTransactionId,
        content: `Hey @[Mentioned User](${mentionedUserId}), please review this.`,
        mentions: [mentionedUserId],
      };

      const response = await request(app)
        .post('/api/notes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(noteData)
        .expect(201);

      expect(response.body.content).toContain('@[Mentioned User]');
      expect(response.body.mentions).toHaveLength(1);
      expect(response.body.mentions[0].userId).toBe(mentionedUserId);
    });

    test('should validate required fields', async () => {
      const invalidData = {
        // Missing required fields
      };

      await request(app)
        .post('/api/notes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });

    test('should validate content length', async () => {
      const invalidData = {
        transactionId: testTransactionId,
        content: '', // Empty content
      };

      await request(app)
        .post('/api/notes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('GET /api/notes', () => {
    test('should get notes with pagination', async () => {
      const response = await request(app)
        .get('/api/notes?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('items');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.items)).toBe(true);
    });

    test('should filter notes by transaction', async () => {
      const response = await request(app)
        .get(`/api/notes?transactionId=${testTransactionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.items.every(note => 
        note.transactionId === testTransactionId
      )).toBe(true);
    });

    test('should search notes by content', async () => {
      const response = await request(app)
        .get('/api/notes?search=test')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.items.some(note => 
        note.content.toLowerCase().includes('test')
      )).toBe(true);
    });
  });

  describe('GET /api/notes/:id', () => {
    test('should get note by ID', async () => {
      const response = await request(app)
        .get(`/api/notes/${testNoteId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: testNoteId,
        content: 'This is a test note',
      });
    });

    test('should return 404 for non-existent note', async () => {
      await request(app)
        .get('/api/notes/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/notes/:id', () => {
    test('should update note', async () => {
      const updateData = {
        content: 'Updated test note content',
      };

      const response = await request(app)
        .put(`/api/notes/${testNoteId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.content).toBe('Updated test note content');
    });

    test('should validate content on update', async () => {
      const invalidData = {
        content: '', // Empty content
      };

      await request(app)
        .put(`/api/notes/${testNoteId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('DELETE /api/notes/:id', () => {
    test('should delete note', async () => {
      await request(app)
        .delete(`/api/notes/${testNoteId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(204);

      // Verify note is deleted
      await request(app)
        .get(`/api/notes/${testNoteId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('GET /api/notes/stats', () => {
    test('should get note statistics', async () => {
      const response = await request(app)
        .get('/api/notes/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('byUser');
      expect(response.body).toHaveProperty('byTransaction');
      expect(response.body).toHaveProperty('totalMentions');
      expect(typeof response.body.total).toBe('number');
    });
  });

  describe('GET /api/notes/mentions/:userId', () => {
    test('should get user mentions', async () => {
      const response = await request(app)
        .get(`/api/notes/mentions/${mentionedUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    test('should filter unread mentions', async () => {
      const response = await request(app)
        .get(`/api/notes/mentions/${mentionedUserId}?unreadOnly=true`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.every(mention => !mention.isRead)).toBe(true);
    });
  });

  describe('PATCH /api/notes/:noteId/mentions/:userId/read', () => {
    test('should mark mention as read', async () => {
      // First create a note with mention
      const noteData = {
        transactionId: testTransactionId,
        content: `@[Mentioned User](${mentionedUserId}) please check this`,
        mentions: [mentionedUserId],
      };

      const noteResponse = await request(app)
        .post('/api/notes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(noteData);

      const noteId = noteResponse.body.id;

      // Mark mention as read
      await request(app)
        .patch(`/api/notes/${noteId}/mentions/${mentionedUserId}/read`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify mention is marked as read
      const mentionsResponse = await request(app)
        .get(`/api/notes/mentions/${mentionedUserId}`)
        .set('Authorization', `Bearer ${authToken}`);

      const mention = mentionsResponse.body.find(m => m.noteId === noteId);
      expect(mention.isRead).toBe(true);
    });
  });

  describe('GET /api/notes/transaction/:transactionId/thread', () => {
    test('should get note thread for transaction', async () => {
      const response = await request(app)
        .get(`/api/notes/transaction/${testTransactionId}/thread`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.every(note => 
        note.transactionId === testTransactionId
      )).toBe(true);
    });
  });

  describe('Authentication & Authorization', () => {
    test('should require authentication', async () => {
      await request(app)
        .get('/api/notes')
        .expect(401);
    });

    test('should reject invalid tokens', async () => {
      await request(app)
        .get('/api/notes')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      const noteData = {
        transactionId: 'invalid-transaction-id',
        content: 'Test note',
      };

      await request(app)
        .post('/api/notes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(noteData)
        .expect(400);
    });

    test('should handle malformed JSON', async () => {
      await request(app)
        .post('/api/notes')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });
  });
});

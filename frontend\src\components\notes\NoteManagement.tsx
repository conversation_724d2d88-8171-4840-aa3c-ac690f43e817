/**
 * NoteManagement component
 * 
 * Main note management interface that combines all note components
 */

'use client';

import { useState } from 'react';
import { Note, NoteSummary, NoteSearchCriteria } from '@/types/note';
import { NoteList } from './NoteList';
import { NoteThread } from './NoteThread';
import { NoteModal } from './NoteModal';
import { Button } from '@/components/ui/Button';

interface NoteManagementProps {
  transactionId?: string;
  title?: string;
  showAddButton?: boolean;
  compact?: boolean;
  defaultView?: 'list' | 'thread';
  currentUserId?: string;
}

export function NoteManagement({
  transactionId,
  title = 'Notes & Communication',
  showAddButton = true,
  compact = false,
  defaultView = 'list',
  currentUserId = 'user-1',
}: NoteManagementProps) {
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'thread'>(defaultView);
  const [searchCriteria, setSearchCriteria] = useState<NoteSearchCriteria>({
    transactionId,
  });

  const handleAddNote = () => {
    setSelectedNote(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleEditNote = (note: NoteSummary) => {
    setSelectedNote(note as Note);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleViewNote = (note: NoteSummary) => {
    setSelectedNote(note as Note);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleDeleteNote = async (noteId: string) => {
    try {
      // TODO: Replace with actual API call
      // await noteApi.deleteNote(noteId);
      console.log('Deleting note:', noteId);
      
      // Mock successful deletion
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // The NoteList/NoteThread components will handle reloading
      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting note:', error);
      throw error;
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedNote(null);
  };

  const handleNoteSave = (note: Note) => {
    // The NoteList/NoteThread components will handle reloading
    console.log('Note saved:', note);
  };

  const handleNoteAdd = (note: Note) => {
    console.log('Note added:', note);
    // Could trigger notifications, refresh, etc.
  };

  const handleNoteEdit = (note: Note) => {
    console.log('Note edited:', note);
    // Could trigger notifications, refresh, etc.
  };

  const toggleViewMode = () => {
    setViewMode(prev => prev === 'list' ? 'thread' : 'list');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        
        <div className="flex items-center space-x-3">
          {/* View Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              📋 List
            </button>
            <button
              onClick={() => setViewMode('thread')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'thread'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              💬 Thread
            </button>
          </div>

          {/* Add Button */}
          {showAddButton && viewMode === 'list' && (
            <Button
              onClick={handleAddNote}
              leftIcon={<span>➕</span>}
            >
              Add Note
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      {viewMode === 'list' ? (
        <NoteList
          transactionId={transactionId}
          searchCriteria={searchCriteria}
          onNoteSelect={handleViewNote}
          onNoteEdit={handleEditNote}
          onNoteDelete={handleDeleteNote}
          showActions={true}
          compact={compact}
          showThread={false}
        />
      ) : (
        <NoteThread
          transactionId={transactionId || ''}
          onNoteAdd={handleNoteAdd}
          onNoteEdit={handleNoteEdit}
          onNoteDelete={handleDeleteNote}
          currentUserId={currentUserId}
          compact={compact}
        />
      )}

      {/* Note Modal */}
      <NoteModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        note={selectedNote}
        transactionId={transactionId}
        mode={modalMode}
        onSave={handleNoteSave}
      />
    </div>
  );
}

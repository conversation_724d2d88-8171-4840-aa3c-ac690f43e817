/**
 * Common validation utilities and schemas
 *
 * Provides reusable validation functions and Jo<PERSON> schemas
 */

import <PERSON><PERSON> from 'joi';
import { UserRole, TransactionStatus, TransactionType, TaskPriority, DocumentCategory } from '@prisma/client';

/**
 * Common validation patterns
 */
export const ValidationPatterns = {
  // Email validation
  email: Joi.string().email().max(255),

  // Password validation (at least 8 characters, with uppercase, lowercase, number)
  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .message('Password must contain at least 8 characters with uppercase, lowercase, and number'),

  // Phone number validation (flexible format)
  phone: Joi.string()
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .message('Invalid phone number format'),

  // Name validation
  name: Joi.string().min(1).max(50).trim(),

  // Address validation
  address: Joi.string().max(200).trim(),

  // UUID validation
  uuid: Joi.string().uuid(),

  // Pagination
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),

  // Search query
  search: Joi.string().max(100).trim(),

  // Date validation
  date: Joi.date().iso(),

  // Currency amount (up to 12 digits with 2 decimal places)
  currency: Joi.number().precision(2).min(0).max(************.99),
};

/**
 * Enum validation schemas
 */
export const EnumSchemas = {
  userRole: Joi.string().valid(...Object.values(UserRole)),
  transactionStatus: Joi.string().valid(...Object.values(TransactionStatus)),
  transactionType: Joi.string().valid(...Object.values(TransactionType)),
  taskPriority: Joi.string().valid(...Object.values(TaskPriority)),
  documentCategory: Joi.string().valid(...Object.values(DocumentCategory)),
};

/**
 * Common validation schemas
 */
export const CommonSchemas = {
  // Pagination query parameters
  pagination: Joi.object({
    page: ValidationPatterns.page,
    limit: ValidationPatterns.limit,
  }),

  // Search query parameters
  search: Joi.object({
    search: ValidationPatterns.search.optional(),
    page: ValidationPatterns.page,
    limit: ValidationPatterns.limit,
  }),

  // ID parameter validation
  idParam: Joi.object({
    id: ValidationPatterns.uuid.required(),
  }),

  // Date range query parameters
  dateRange: Joi.object({
    startDate: ValidationPatterns.date.optional(),
    endDate: ValidationPatterns.date.optional(),
  }).custom((value, helpers) => {
    if (value.startDate && value.endDate && value.startDate > value.endDate) {
      return helpers.error('date.range');
    }
    return value;
  }).messages({
    'date.range': 'Start date must be before end date',
  }),
};

/**
 * User-related validation schemas
 */
export const UserSchemas = {
  // User registration
  register: Joi.object({
    email: ValidationPatterns.email.required(),
    password: ValidationPatterns.password.required(),
    firstName: ValidationPatterns.name.required(),
    lastName: ValidationPatterns.name.required(),
    phone: ValidationPatterns.phone.optional().allow(''),
    role: EnumSchemas.userRole.required(),
    brokerageId: ValidationPatterns.uuid.optional(),
  }),

  // User login
  login: Joi.object({
    email: ValidationPatterns.email.required(),
    password: Joi.string().required(),
  }),

  // User profile update
  updateProfile: Joi.object({
    firstName: ValidationPatterns.name.optional(),
    lastName: ValidationPatterns.name.optional(),
    phone: ValidationPatterns.phone.optional().allow(''),
    role: EnumSchemas.userRole.optional(),
    brokerageId: ValidationPatterns.uuid.optional().allow(null),
    isActive: Joi.boolean().optional(),
  }),

  // Password change
  changePassword: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: ValidationPatterns.password.required(),
  }),

  // User search
  search: Joi.object({
    role: EnumSchemas.userRole.optional(),
    brokerageId: ValidationPatterns.uuid.optional(),
    isActive: Joi.boolean().optional(),
    search: ValidationPatterns.search.optional(),
    page: ValidationPatterns.page,
    limit: ValidationPatterns.limit,
  }),
};

/**
 * Brokerage-related validation schemas
 */
export const BrokerageSchemas = {
  // Brokerage creation
  create: Joi.object({
    name: Joi.string().min(1).max(100).required(),
    address: ValidationPatterns.address.optional().allow(''),
    phone: ValidationPatterns.phone.optional().allow(''),
    email: ValidationPatterns.email.optional().allow(''),
    settings: Joi.object().optional(),
  }),

  // Brokerage update
  update: Joi.object({
    name: Joi.string().min(1).max(100).optional(),
    address: ValidationPatterns.address.optional().allow(''),
    phone: ValidationPatterns.phone.optional().allow(''),
    email: ValidationPatterns.email.optional().allow(''),
    settings: Joi.object().optional(),
  }),

  // Brokerage search
  search: Joi.object({
    search: ValidationPatterns.search.optional(),
    page: ValidationPatterns.page,
    limit: ValidationPatterns.limit,
  }),
};

/**
 * Transaction-related validation schemas
 */
export const TransactionSchemas = {
  // Transaction creation
  create: Joi.object({
    propertyAddress: Joi.string().min(1).max(200).required(),
    transactionType: EnumSchemas.transactionType.required(),
    contractDate: ValidationPatterns.date.optional(),
    closingDate: ValidationPatterns.date.optional(),
    salePrice: ValidationPatterns.currency.optional(),
    buyerName: Joi.string().max(100).optional().allow(''),
    sellerName: Joi.string().max(100).optional().allow(''),
    tcId: ValidationPatterns.uuid.optional(),
    listingAgentId: ValidationPatterns.uuid.optional(),
    sellingAgentId: ValidationPatterns.uuid.optional(),
    notes: Joi.string().max(1000).optional().allow(''),
  }),

  // Transaction update
  update: Joi.object({
    propertyAddress: Joi.string().min(1).max(200).optional(),
    transactionType: EnumSchemas.transactionType.optional(),
    status: EnumSchemas.transactionStatus.optional(),
    contractDate: ValidationPatterns.date.optional().allow(null),
    closingDate: ValidationPatterns.date.optional().allow(null),
    salePrice: ValidationPatterns.currency.optional().allow(null),
    buyerName: Joi.string().max(100).optional().allow(''),
    sellerName: Joi.string().max(100).optional().allow(''),
    tcId: ValidationPatterns.uuid.optional().allow(null),
    listingAgentId: ValidationPatterns.uuid.optional().allow(null),
    sellingAgentId: ValidationPatterns.uuid.optional().allow(null),
    notes: Joi.string().max(1000).optional().allow(''),
  }),

  // Transaction search
  search: Joi.object({
    status: Joi.array().items(EnumSchemas.transactionStatus).optional(),
    transactionType: Joi.array().items(EnumSchemas.transactionType).optional(),
    tcId: ValidationPatterns.uuid.optional(),
    listingAgentId: ValidationPatterns.uuid.optional(),
    sellingAgentId: ValidationPatterns.uuid.optional(),
    brokerageId: ValidationPatterns.uuid.optional(),
    contractDateFrom: ValidationPatterns.date.optional(),
    contractDateTo: ValidationPatterns.date.optional(),
    closingDateFrom: ValidationPatterns.date.optional(),
    closingDateTo: ValidationPatterns.date.optional(),
    search: ValidationPatterns.search.optional(),
    sortBy: Joi.string().valid('propertyAddress', 'contractDate', 'closingDate', 'status', 'createdAt').optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional(),
    page: ValidationPatterns.page,
    limit: ValidationPatterns.limit,
  }),
};

/**
 * Task-related validation schemas
 */
export const TaskSchemas = {
  // Task creation
  create: Joi.object({
    transactionId: ValidationPatterns.uuid.required(),
    title: Joi.string().min(1).max(200).required(),
    description: Joi.string().max(1000).optional().allow(''),
    dueDate: ValidationPatterns.date.required(),
    priority: EnumSchemas.taskPriority.required(),
    category: Joi.string().max(50).optional().allow(''),
    isRequired: Joi.boolean().default(false),
    assignedToId: ValidationPatterns.uuid.optional(),
    taskTemplateId: ValidationPatterns.uuid.optional(),
  }),

  // Task update
  update: Joi.object({
    title: Joi.string().min(1).max(200).optional(),
    description: Joi.string().max(1000).optional().allow(''),
    dueDate: ValidationPatterns.date.optional(),
    priority: EnumSchemas.taskPriority.optional(),
    category: Joi.string().max(50).optional().allow(''),
    isRequired: Joi.boolean().optional(),
    assignedToId: ValidationPatterns.uuid.optional().allow(null),
  }),

  // Task search
  search: Joi.object({
    transactionId: ValidationPatterns.uuid.optional(),
    assignedToId: ValidationPatterns.uuid.optional(),
    createdById: ValidationPatterns.uuid.optional(),
    priority: Joi.array().items(EnumSchemas.taskPriority).optional(),
    category: Joi.array().items(Joi.string()).optional(),
    isCompleted: Joi.boolean().optional(),
    isOverdue: Joi.boolean().optional(),
    dueDateFrom: ValidationPatterns.date.optional(),
    dueDateTo: ValidationPatterns.date.optional(),
    search: ValidationPatterns.search.optional(),
    sortBy: Joi.string().valid('title', 'dueDate', 'priority', 'createdAt').optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional(),
    page: ValidationPatterns.page,
    limit: ValidationPatterns.limit,
  }),

  // Bulk task operation
  bulkOperation: Joi.object({
    taskIds: Joi.array().items(ValidationPatterns.uuid).min(1).required(),
    operation: Joi.string().valid('complete', 'assign', 'update_priority', 'update_due_date').required(),
    data: Joi.object({
      assignedToId: ValidationPatterns.uuid.optional(),
      priority: EnumSchemas.taskPriority.optional(),
      dueDate: ValidationPatterns.date.optional(),
      completedAt: ValidationPatterns.date.optional(),
    }).optional(),
  }),
};

/**
 * Task template-related validation schemas
 */
export const TaskTemplateSchemas = {
  // Task template creation
  create: Joi.object({
    name: Joi.string().min(1).max(200).required(),
    description: Joi.string().max(1000).optional().allow(''),
    defaultDueDays: Joi.number().integer().min(0).max(365).required(),
    transactionType: EnumSchemas.transactionType.required(),
    category: Joi.string().max(50).optional().allow(''),
    priority: EnumSchemas.taskPriority.required(),
    isRequired: Joi.boolean().default(false),
  }),

  // Task template update
  update: Joi.object({
    name: Joi.string().min(1).max(200).optional(),
    description: Joi.string().max(1000).optional().allow(''),
    defaultDueDays: Joi.number().integer().min(0).max(365).optional(),
    transactionType: EnumSchemas.transactionType.optional(),
    category: Joi.string().max(50).optional().allow(''),
    priority: EnumSchemas.taskPriority.optional(),
    isRequired: Joi.boolean().optional(),
  }),

  // Task template search
  search: Joi.object({
    transactionType: EnumSchemas.transactionType.optional(),
    category: Joi.string().optional(),
    priority: EnumSchemas.taskPriority.optional(),
    isRequired: Joi.boolean().optional(),
    search: ValidationPatterns.search.optional(),
    page: ValidationPatterns.page,
    limit: ValidationPatterns.limit,
  }),
};

/**
 * Document-related validation schemas
 */
export const DocumentSchemas = {
  // Document upload
  upload: Joi.object({
    transactionId: ValidationPatterns.uuid.required(),
    filename: Joi.string().min(1).max(200).required(),
    originalFilename: Joi.string().min(1).max(200).required(),
    category: EnumSchemas.documentCategory.required(),
  }),

  // Document update
  update: Joi.object({
    filename: Joi.string().min(1).max(200).optional(),
    originalFilename: Joi.string().min(1).max(200).optional(),
    category: EnumSchemas.documentCategory.optional(),
    isSigned: Joi.boolean().optional(),
    docusignEnvelopeId: Joi.string().optional().allow(null),
  }),

  // Document search
  search: Joi.object({
    transactionId: ValidationPatterns.uuid.optional(),
    uploadedById: ValidationPatterns.uuid.optional(),
    category: Joi.array().items(EnumSchemas.documentCategory).optional(),
    isSigned: Joi.boolean().optional(),
    search: ValidationPatterns.search.optional(),
    sortBy: Joi.string().valid('filename', 'category', 'uploadedAt', 'fileSize').optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional(),
    page: ValidationPatterns.page,
    limit: ValidationPatterns.limit,
  }),

  // Bulk document operation
  bulkOperation: Joi.object({
    documentIds: Joi.array().items(ValidationPatterns.uuid).min(1).required(),
    operation: Joi.string().valid('delete', 'update_category', 'sign').required(),
    data: Joi.object({
      category: EnumSchemas.documentCategory.optional(),
      isSigned: Joi.boolean().optional(),
    }).optional(),
  }),
};

/**
 * Validation helper functions
 */
export const ValidationHelpers = {
  /**
   * Validate request body against schema
   */
  validateBody: (schema: Joi.ObjectSchema, data: any) => {
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const message = error.details.map(detail => detail.message).join(', ');
      throw new Error(`Validation error: ${message}`);
    }

    return value;
  },

  /**
   * Validate query parameters against schema
   */
  validateQuery: (schema: Joi.ObjectSchema, data: any) => {
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      convert: true,
    });

    if (error) {
      const message = error.details.map(detail => detail.message).join(', ');
      throw new Error(`Query validation error: ${message}`);
    }

    return value;
  },

  /**
   * Validate route parameters against schema
   */
  validateParams: (schema: Joi.ObjectSchema, data: any) => {
    const { error, value } = schema.validate(data, {
      abortEarly: false,
    });

    if (error) {
      const message = error.details.map(detail => detail.message).join(', ');
      throw new Error(`Parameter validation error: ${message}`);
    }

    return value;
  },

  /**
   * Check if string is a valid UUID
   */
  isValidUUID: (str: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  },

  /**
   * Sanitize string input
   */
  sanitizeString: (str: string): string => {
    return str.trim().replace(/\s+/g, ' ');
  },

  /**
   * Validate email format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate phone number format
   */
  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  },
};

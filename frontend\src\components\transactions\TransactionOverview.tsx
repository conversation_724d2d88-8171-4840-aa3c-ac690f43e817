/**
 * TransactionOverview component
 * 
 * Overview tab content showing transaction summary and key metrics
 */

'use client';

import { ContactManagement } from '@/components/contacts';
import { NoteManagement } from '@/components/notes';

interface TransactionOverviewProps {
  transaction: {
    id: string;
    propertyAddress: string;
    transactionType: string;
    status: string;
    contractDate?: Date;
    closingDate?: Date;
    salePrice?: number;
    buyerName?: string;
    sellerName?: string;
    notes?: string;
    createdAt: Date;
    updatedAt: Date;
    brokerage: {
      id: string;
      name: string;
    };
    tc?: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    listingAgent?: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    sellingAgent?: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    taskCount: number;
    completedTaskCount: number;
    documentCount: number;
    contactCount: number;
    noteCount: number;
  };
}

export function TransactionOverview({ transaction }: TransactionOverviewProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const progressPercentage = Math.round((transaction.completedTaskCount / transaction.taskCount) * 100);

  return (
    <div className="space-y-8">
      {/* Transaction Details Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Property Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Property Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Address</label>
                <p className="mt-1 text-sm text-gray-900">{transaction.propertyAddress}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Transaction Type</label>
                <p className="mt-1 text-sm text-gray-900">{transaction.transactionType}</p>
              </div>
              {transaction.salePrice && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Sale Price</label>
                  <p className="mt-1 text-sm font-semibold text-gray-900">
                    {formatCurrency(transaction.salePrice)}
                  </p>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-500">Status</label>
                <p className="mt-1 text-sm text-gray-900">{transaction.status.replace('_', ' ')}</p>
              </div>
            </div>
          </div>

          {/* Parties */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Parties</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {transaction.buyerName && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Buyer</label>
                  <p className="mt-1 text-sm text-gray-900">{transaction.buyerName}</p>
                </div>
              )}
              {transaction.sellerName && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Seller</label>
                  <p className="mt-1 text-sm text-gray-900">{transaction.sellerName}</p>
                </div>
              )}
            </div>
          </div>

          {/* Important Dates */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Important Dates</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {transaction.contractDate && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Contract Date</label>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(transaction.contractDate)}</p>
                </div>
              )}
              {transaction.closingDate && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Closing Date</label>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(transaction.closingDate)}</p>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-500">Created</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(transaction.createdAt)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Last Updated</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(transaction.updatedAt)}</p>
              </div>
            </div>
          </div>

          {/* Team */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Team</h3>
            <div className="space-y-3">
              {transaction.tc && (
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {transaction.tc.firstName} {transaction.tc.lastName}
                    </p>
                    <p className="text-sm text-gray-500">Transaction Coordinator</p>
                  </div>
                  <a
                    href={`mailto:${transaction.tc.email}`}
                    className="text-blue-600 hover:text-blue-700 text-sm"
                  >
                    {transaction.tc.email}
                  </a>
                </div>
              )}
              {transaction.listingAgent && (
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {transaction.listingAgent.firstName} {transaction.listingAgent.lastName}
                    </p>
                    <p className="text-sm text-gray-500">Listing Agent</p>
                  </div>
                  <a
                    href={`mailto:${transaction.listingAgent.email}`}
                    className="text-blue-600 hover:text-blue-700 text-sm"
                  >
                    {transaction.listingAgent.email}
                  </a>
                </div>
              )}
              {transaction.sellingAgent && (
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {transaction.sellingAgent.firstName} {transaction.sellingAgent.lastName}
                    </p>
                    <p className="text-sm text-gray-500">Selling Agent</p>
                  </div>
                  <a
                    href={`mailto:${transaction.sellingAgent.email}`}
                    className="text-blue-600 hover:text-blue-700 text-sm"
                  >
                    {transaction.sellingAgent.email}
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          {transaction.notes && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Notes</h3>
              <p className="text-sm text-gray-700 whitespace-pre-wrap">{transaction.notes}</p>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Progress Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Progress</h3>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {progressPercentage}%
              </div>
              <div className="text-sm text-gray-500 mb-4">
                {transaction.completedTaskCount} of {transaction.taskCount} tasks completed
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Contacts</span>
                <span className="text-sm font-medium text-gray-900">{transaction.contactCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Documents</span>
                <span className="text-sm font-medium text-gray-900">{transaction.documentCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Notes</span>
                <span className="text-sm font-medium text-gray-900">{transaction.noteCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Tasks</span>
                <span className="text-sm font-medium text-gray-900">{transaction.taskCount}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Contacts */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Recent Contacts</h3>
            <a href="#contacts" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              View All
            </a>
          </div>
          <ContactManagement
            transactionId={transaction.id}
            title=""
            showAddButton={false}
            compact={true}
          />
        </div>

        {/* Recent Communication */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Recent Communication</h3>
            <a href="#communication" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              View All
            </a>
          </div>
          <NoteManagement
            transactionId={transaction.id}
            title=""
            showAddButton={false}
            compact={true}
            defaultView="list"
            currentUserId="user-1"
          />
        </div>
      </div>
    </div>
  );
}

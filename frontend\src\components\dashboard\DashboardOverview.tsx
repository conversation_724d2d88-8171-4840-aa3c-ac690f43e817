/**
 * Dashboard overview component
 */

'use client';

import { useAuthStore } from '@/store/authStore';
import { StatsCard } from './StatsCard';
import { RecentActivity } from './RecentActivity';
import { UpcomingDeadlines } from './UpcomingDeadlines';
import { QuickActions } from './QuickActions';
import { RecentCommunication } from './RecentCommunication';
import { QuickCommunication } from './QuickCommunication';
import { CommunicationStats } from './CommunicationStats';
import {
  DocumentTextIcon,
  CheckSquareIcon,
  FolderIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const mockStats = [
  {
    name: 'Active Transactions',
    value: '12',
    change: '+2',
    changeType: 'increase' as const,
    icon: DocumentTextIcon,
  },
  {
    name: 'Pending Tasks',
    value: '8',
    change: '-3',
    changeType: 'decrease' as const,
    icon: CheckSquareIcon,
  },
  {
    name: 'Documents',
    value: '156',
    change: '+12',
    changeType: 'increase' as const,
    icon: FolderIcon,
  },
  {
    name: 'Active Contacts',
    value: '34',
    change: '+5',
    changeType: 'increase' as const,
    icon: UsersIcon,
  },
];

const mockRecentActivity = [
  {
    id: '1',
    type: 'transaction_created',
    title: 'New transaction created',
    description: '123 Main St, Philadelphia, PA',
    timestamp: '2 hours ago',
    user: 'John Doe',
  },
  {
    id: '2',
    type: 'task_completed',
    title: 'Task completed',
    description: 'Property inspection scheduled',
    timestamp: '4 hours ago',
    user: 'Sarah Johnson',
  },
  {
    id: '3',
    type: 'document_uploaded',
    title: 'Document uploaded',
    description: 'Purchase agreement signed',
    timestamp: '6 hours ago',
    user: 'Mike Chen',
  },
];

const mockUpcomingDeadlines = [
  {
    id: '1',
    title: 'Property Inspection',
    transaction: '123 Main St',
    dueDate: '2024-01-15',
    priority: 'high' as const,
    daysUntil: 2,
  },
  {
    id: '2',
    title: 'Financing Approval',
    transaction: '456 Oak Ave',
    dueDate: '2024-01-18',
    priority: 'medium' as const,
    daysUntil: 5,
  },
  {
    id: '3',
    title: 'Final Walkthrough',
    transaction: '789 Pine St',
    dueDate: '2024-01-20',
    priority: 'high' as const,
    daysUntil: 7,
  },
];

export function DashboardOverview() {
  const { user } = useAuthStore();

  return (
    <div className="space-y-6">
      {/* Welcome message */}
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="text-gray-600 mt-1">
          Here's what's happening with your transactions today.
        </p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {mockStats.map((stat) => (
          <StatsCard key={stat.name} {...stat} />
        ))}
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Recent activity and communication */}
        <div className="lg:col-span-2 space-y-6">
          <RecentActivity activities={mockRecentActivity} />
          <RecentCommunication currentUserId={user?.id} />
          <UpcomingDeadlines deadlines={mockUpcomingDeadlines} />
        </div>

        {/* Right column - Quick actions and communication */}
        <div className="lg:col-span-1 space-y-6">
          <QuickActions />
          <QuickCommunication currentUserId={user?.id} />
          <CommunicationStats currentUserId={user?.id} />
        </div>
      </div>
    </div>
  );
}

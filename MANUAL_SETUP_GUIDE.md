# Manual Setup Guide for TC Platform

Since the automated setup is having PostgreSQL authentication issues, here's a manual setup guide:

## 🔧 Quick Fix Steps

### 1. Set PostgreSQL Password
The issue is that PostgreSQL needs a password for the `postgres` user. Here's how to fix it:

**Option A: Reset postgres password via Windows**
1. Open Command Prompt as Administrator
2. Run: `"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres`
3. If it asks for password, try: `tc_password_2024` or just press Enter
4. If you get in, run: `ALTER USER postgres PASSWORD 'tc_password_2024';`
5. Type `\q` to quit

**Option B: Use pgAdmin (if installed)**
1. Open pgAdmin
2. Connect to PostgreSQL server
3. Right-click on "postgres" user → Properties
4. Set password to: `tc_password_2024`

### 2. Create Database Manually
Once you can connect to PostgreSQL:

```sql
CREATE DATABASE tc_platform;
CREATE USER tc_user WITH PASSWORD 'tc_password_2024';
GRANT ALL PRIVILEGES ON DATABASE tc_platform TO tc_user;
ALTER USER tc_user CREATEDB;
```

### 3. Run Backend Setup
```bash
cd backend
npx prisma generate
npx prisma db push
npm run dev
```

### 4. Run Frontend (in new terminal)
```bash
cd frontend
npm run dev
```

## 🎯 Expected Results

- Backend running on: http://localhost:3001
- Frontend running on: http://localhost:3000
- Demo page: http://localhost:3000/demo/contact-communication

## 🐛 Common Issues

### CSS Error Fixed
✅ The circular dependency in `globals.css` has been fixed

### Database Connection
If you get database connection errors:
1. Check PostgreSQL service is running: `Get-Service postgresql*`
2. Verify connection string in `backend/.env`
3. Test connection: `"C:\Program Files\PostgreSQL\17\bin\pg_isready.exe" -h localhost -p 5432`

### Environment Files
✅ Both `.env` files have been created:
- `backend/.env` - Database and API configuration
- `frontend/.env.local` - Frontend configuration

## 🚀 Alternative: Use SQLite for Quick Testing

If PostgreSQL continues to be problematic, you can temporarily use SQLite:

1. Edit `backend/prisma/schema.prisma`:
```prisma
datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}
```

2. Update `backend/.env`:
```env
DATABASE_URL="file:./dev.db"
```

3. Run:
```bash
npx prisma generate
npx prisma db push
```

This will get you up and running quickly for testing the Contact & Communication features!

## 📞 Next Steps

Once the database is working:
1. Test the Contact Management demo
2. Test the Note/Communication features
3. Verify all CRUD operations work
4. Check the API endpoints at http://localhost:3001/api/docs (if Swagger is set up)

/**
 * React Query hooks for contact management
 */

import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { contactApi } from '@/lib/api';
import {
  Contact,
  ContactSummary,
  CreateContactData,
  UpdateContactData,
  ContactSearchCriteria,
  ContactListResponse,
  ContactStats,
  BulkContactOperation,
} from '@/types/contact';

/**
 * Query keys for contact-related queries
 */
export const contactKeys = {
  all: ['contacts'] as const,
  lists: () => [...contactKeys.all, 'list'] as const,
  list: (criteria: ContactSearchCriteria) => [...contactKeys.lists(), criteria] as const,
  details: () => [...contactKeys.all, 'detail'] as const,
  detail: (id: string) => [...contactKeys.details(), id] as const,
  stats: (transactionId?: string) => [...contactKeys.all, 'stats', transactionId] as const,
  roles: () => [...contactKeys.all, 'roles'] as const,
  byTransaction: (transactionId: string) => [...contactKeys.all, 'transaction', transactionId] as const,
  byRole: (role: string, transactionId?: string) => [...contactKeys.all, 'role', role, transactionId] as const,
  suggestions: (transactionId: string) => [...contactKeys.all, 'suggestions', transactionId] as const,
};

/**
 * Hook to get contacts with search and pagination
 */
export function useContacts(criteria: ContactSearchCriteria = {}) {
  return useQuery({
    queryKey: contactKeys.list(criteria),
    queryFn: () => contactApi.getContacts(criteria),
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get a single contact by ID
 */
export function useContact(id: string, enabled = true) {
  return useQuery({
    queryKey: contactKeys.detail(id),
    queryFn: () => contactApi.getContact(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get contact statistics
 */
export function useContactStats(transactionId?: string) {
  return useQuery({
    queryKey: contactKeys.stats(transactionId),
    queryFn: () => contactApi.getContactStats(transactionId),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get available contact roles
 */
export function useContactRoles() {
  return useQuery({
    queryKey: contactKeys.roles(),
    queryFn: () => contactApi.getContactRoles(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Hook to get contacts by transaction
 */
export function useContactsByTransaction(transactionId: string, enabled = true) {
  return useQuery({
    queryKey: contactKeys.byTransaction(transactionId),
    queryFn: () => contactApi.getContactsByTransaction(transactionId),
    enabled: enabled && !!transactionId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get contacts by role
 */
export function useContactsByRole(role: string, transactionId?: string, enabled = true) {
  return useQuery({
    queryKey: contactKeys.byRole(role, transactionId),
    queryFn: () => contactApi.getContactsByRole(role, transactionId),
    enabled: enabled && !!role,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get contact suggestions for mentions
 */
export function useContactSuggestions(transactionId: string, enabled = true) {
  return useQuery({
    queryKey: contactKeys.suggestions(transactionId),
    queryFn: () => contactApi.getContactSuggestions(transactionId),
    enabled: enabled && !!transactionId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to create a new contact
 */
export function useCreateContact() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateContactData) => contactApi.createContact(data),
    onSuccess: (newContact) => {
      // Invalidate and refetch contact lists
      queryClient.invalidateQueries(contactKeys.lists());
      queryClient.invalidateQueries(contactKeys.stats());
      queryClient.invalidateQueries(contactKeys.byTransaction(newContact.transactionId));
      queryClient.invalidateQueries(contactKeys.suggestions(newContact.transactionId));

      toast.success('Contact created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create contact');
    },
  });
}

/**
 * Hook to update a contact
 */
export function useUpdateContact() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateContactData }) =>
      contactApi.updateContact(id, data),
    onSuccess: (updatedContact) => {
      // Update the contact in the cache
      queryClient.setQueryData(contactKeys.detail(updatedContact.id), updatedContact);
      
      // Invalidate related queries
      queryClient.invalidateQueries(contactKeys.lists());
      queryClient.invalidateQueries(contactKeys.stats());
      queryClient.invalidateQueries(contactKeys.byTransaction(updatedContact.transactionId));
      queryClient.invalidateQueries(contactKeys.suggestions(updatedContact.transactionId));

      toast.success('Contact updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update contact');
    },
  });
}

/**
 * Hook to delete a contact
 */
export function useDeleteContact() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => contactApi.deleteContact(id),
    onSuccess: (_, deletedId) => {
      // Remove the contact from the cache
      queryClient.removeQueries(contactKeys.detail(deletedId));
      
      // Invalidate related queries
      queryClient.invalidateQueries(contactKeys.lists());
      queryClient.invalidateQueries(contactKeys.stats());
      queryClient.invalidateQueries(contactKeys.all);

      toast.success('Contact deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete contact');
    },
  });
}

/**
 * Hook to perform bulk operations on contacts
 */
export function useBulkContactOperation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (operation: BulkContactOperation) => contactApi.bulkOperation(operation),
    onSuccess: (_, operation) => {
      // Invalidate all contact queries
      queryClient.invalidateQueries(contactKeys.all);

      const operationNames = {
        delete: 'deleted',
        update_role: 'updated',
        update_company: 'updated',
        export: 'exported',
      };

      const operationName = operationNames[operation.operation] || 'processed';
      toast.success(`${operation.contactIds.length} contact(s) ${operationName} successfully`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to perform bulk operation');
    },
  });
}

/**
 * Hook to export contacts
 */
export function useExportContacts() {
  return useMutation({
    mutationFn: (criteria: ContactSearchCriteria) => contactApi.exportContacts(criteria),
    onSuccess: (blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `contacts-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Contacts exported successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to export contacts');
    },
  });
}

/**
 * Hook to search contacts with debouncing
 */
export function useSearchContacts(query: string, transactionId?: string, enabled = true) {
  return useQuery({
    queryKey: [...contactKeys.all, 'search', query, transactionId],
    queryFn: () => contactApi.searchContacts(query, transactionId),
    enabled: enabled && query.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
}

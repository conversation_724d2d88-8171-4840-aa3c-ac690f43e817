/**
 * Contact API Integration Tests
 * 
 * Comprehensive tests for all contact API endpoints
 */

const request = require('supertest');
const app = require('../../src/app');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

describe('Contact API Integration Tests', () => {
  let authToken;
  let testBrokerageId;
  let testTransactionId;
  let testContactId;

  beforeAll(async () => {
    // Setup test data
    const testBrokerage = await prisma.brokerage.create({
      data: {
        name: 'Test Brokerage',
        address: '123 Test St',
        phone: '555-0123',
        email: '<EMAIL>',
      },
    });
    testBrokerageId = testBrokerage.id;

    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'TC',
        brokerageId: testBrokerageId,
      },
    });

    const testTransaction = await prisma.transaction.create({
      data: {
        propertyAddress: '123 Test Property St',
        transactionType: 'PURCHASE',
        status: 'ACTIVE',
        brokerageId: testBrokerageId,
        tcId: testUser.id,
      },
    });
    testTransactionId = testTransaction.id;

    // Mock authentication token
    authToken = 'mock-jwt-token';
  });

  afterAll(async () => {
    // Cleanup test data
    await prisma.contact.deleteMany({ where: { transactionId: testTransactionId } });
    await prisma.transaction.deleteMany({ where: { id: testTransactionId } });
    await prisma.user.deleteMany({ where: { brokerageId: testBrokerageId } });
    await prisma.brokerage.deleteMany({ where: { id: testBrokerageId } });
    await prisma.$disconnect();
  });

  describe('POST /api/contacts', () => {
    test('should create a new contact', async () => {
      const contactData = {
        transactionId: testTransactionId,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '555-0123',
        role: 'buyer',
        company: 'Test Company',
      };

      const response = await request(app)
        .post('/api/contacts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(contactData)
        .expect(201);

      expect(response.body).toMatchObject({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        role: 'buyer',
      });

      testContactId = response.body.id;
    });

    test('should validate required fields', async () => {
      const invalidData = {
        transactionId: testTransactionId,
        // Missing required fields
      };

      await request(app)
        .post('/api/contacts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });

    test('should validate email format', async () => {
      const invalidData = {
        transactionId: testTransactionId,
        firstName: 'John',
        lastName: 'Doe',
        email: 'invalid-email',
        role: 'buyer',
      };

      await request(app)
        .post('/api/contacts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('GET /api/contacts', () => {
    test('should get contacts with pagination', async () => {
      const response = await request(app)
        .get('/api/contacts?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('items');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.items)).toBe(true);
    });

    test('should filter contacts by transaction', async () => {
      const response = await request(app)
        .get(`/api/contacts?transactionId=${testTransactionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.items.every(contact => 
        contact.transactionId === testTransactionId
      )).toBe(true);
    });

    test('should search contacts by name', async () => {
      const response = await request(app)
        .get('/api/contacts?search=John')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.items.some(contact => 
        contact.firstName.includes('John') || contact.lastName.includes('John')
      )).toBe(true);
    });
  });

  describe('GET /api/contacts/:id', () => {
    test('should get contact by ID', async () => {
      const response = await request(app)
        .get(`/api/contacts/${testContactId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: testContactId,
        firstName: 'John',
        lastName: 'Doe',
      });
    });

    test('should return 404 for non-existent contact', async () => {
      await request(app)
        .get('/api/contacts/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/contacts/:id', () => {
    test('should update contact', async () => {
      const updateData = {
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '555-9999',
      };

      const response = await request(app)
        .put(`/api/contacts/${testContactId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '555-9999',
      });
    });

    test('should validate email format on update', async () => {
      const invalidData = {
        email: 'invalid-email-format',
      };

      await request(app)
        .put(`/api/contacts/${testContactId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('DELETE /api/contacts/:id', () => {
    test('should delete contact', async () => {
      await request(app)
        .delete(`/api/contacts/${testContactId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(204);

      // Verify contact is deleted
      await request(app)
        .get(`/api/contacts/${testContactId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('GET /api/contacts/stats', () => {
    test('should get contact statistics', async () => {
      const response = await request(app)
        .get('/api/contacts/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('byRole');
      expect(response.body).toHaveProperty('byTransaction');
      expect(typeof response.body.total).toBe('number');
    });
  });

  describe('GET /api/contacts/roles', () => {
    test('should get available contact roles', async () => {
      const response = await request(app)
        .get('/api/contacts/roles')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body).toContain('buyer');
      expect(response.body).toContain('seller');
      expect(response.body).toContain('agent');
    });
  });

  describe('Authentication', () => {
    test('should require authentication', async () => {
      await request(app)
        .get('/api/contacts')
        .expect(401);
    });

    test('should reject invalid tokens', async () => {
      await request(app)
        .get('/api/contacts')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      // Mock database error by using invalid transaction ID
      const contactData = {
        transactionId: 'invalid-transaction-id',
        firstName: 'Test',
        lastName: 'User',
        role: 'buyer',
      };

      await request(app)
        .post('/api/contacts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(contactData)
        .expect(400);
    });

    test('should handle malformed JSON', async () => {
      await request(app)
        .post('/api/contacts')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });
  });
});

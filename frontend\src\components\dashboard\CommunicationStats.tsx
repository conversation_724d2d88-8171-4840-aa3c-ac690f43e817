/**
 * CommunicationStats component
 * 
 * Dashboard widget showing communication statistics and metrics
 */

'use client';

import Link from 'next/link';
import { useNoteStats, useUnreadMentionsCount } from '@/hooks';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface CommunicationStatsProps {
  currentUserId?: string;
}

export function CommunicationStats({ currentUserId = 'user-1' }: CommunicationStatsProps) {
  // React Query hooks for real-time data
  const { data: noteStats, isLoading: loadingStats } = useNoteStats();
  const { data: unreadCount = 0 } = useUnreadMentionsCount(currentUserId);

  // Mock data for demonstration
  const mockStats = {
    total: 156,
    byUser: {
      'user-1': 45,
      'user-2': 38,
      'user-3': 32,
      'user-4': 25,
      'user-5': 16,
    },
    byTransaction: {
      'trans-001': 67,
      'trans-002': 43,
      'trans-003': 28,
      'trans-004': 18,
    },
    totalMentions: 23,
    recentActivity: 12,
    averageNotesPerTransaction: 8.7,
    mostActiveUsers: [
      { userId: 'user-1', userName: '<PERSON>', noteCount: 45 },
      { userId: 'user-2', userName: 'Mike Johnson', noteCount: 38 },
      { userId: 'user-3', userName: 'Lisa Rodriguez', noteCount: 32 },
    ],
  };

  const stats = noteStats || mockStats;

  const getActivityLevel = (count: number) => {
    if (count >= 40) return { level: 'High', color: 'text-green-600', bg: 'bg-green-100' };
    if (count >= 20) return { level: 'Medium', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    return { level: 'Low', color: 'text-gray-600', bg: 'bg-gray-100' };
  };

  if (loadingStats) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
            <span className="ml-2 text-gray-600">Loading stats...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Communication Stats
          </h3>
          <Link
            href="/demo/contact-communication"
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            View Details →
          </Link>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-sm text-blue-800">Total Messages</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{unreadCount}</div>
            <div className="text-sm text-purple-800">Unread Mentions</div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-900">Recent Activity</h4>
            <span className="text-xs text-gray-500">Last 24 hours</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min((stats.recentActivity / 20) * 100, 100)}%` }}
              />
            </div>
            <span className="text-sm font-medium text-gray-900">{stats.recentActivity}</span>
          </div>
        </div>

        {/* Most Active Users */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Most Active</h4>
          <div className="space-y-2">
            {stats.mostActiveUsers.slice(0, 3).map((user, index) => {
              const activity = getActivityLevel(user.noteCount);
              return (
                <div key={user.userId} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-medium">
                        {user.userName.split(' ').map(n => n.charAt(0)).join('')}
                      </span>
                    </div>
                    <span className="text-sm text-gray-900">{user.userName}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">{user.noteCount}</span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${activity.bg} ${activity.color}`}>
                      {activity.level}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-2">
          <Link
            href="/dashboard/transactions?tab=communication"
            className="block w-full text-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
          >
            📊 View All Communications
          </Link>
          <div className="grid grid-cols-2 gap-2">
            <button className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
              💬 New Message
            </button>
            <button className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
              🔔 Notifications
            </button>
          </div>
        </div>

        {/* Footer Info */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Avg: {stats.averageNotesPerTransaction} notes/transaction</span>
            <span>{stats.totalMentions} total mentions</span>
          </div>
        </div>
      </div>
    </div>
  );
}

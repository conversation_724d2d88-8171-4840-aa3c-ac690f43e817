/**
 * NoteForm component
 * 
 * Form for creating and editing notes with mention support
 */

'use client';

import { useState, useEffect } from 'react';
import { NoteFormProps, CreateNoteData, UpdateNoteData, NoteFormErrors, NoteValidation } from '@/types/note';
import { MentionInput } from './MentionInput';
import { Button } from '@/components/ui/Button';

export function NoteForm({
  note,
  transactionId,
  onSubmit,
  onCancel,
  loading = false,
  errors = {},
  placeholder = 'Write a note...',
  showMentions = true,
  availableUsers = [],
}: NoteFormProps) {
  const [content, setContent] = useState(note?.content || '');
  const [mentions, setMentions] = useState<string[]>(note?.mentions || []);
  const [localErrors, setLocalErrors] = useState<NoteFormErrors>({});
  
  const isEditing = !!note;

  useEffect(() => {
    setLocalErrors(errors);
  }, [errors]);

  const handleContentChange = (newContent: string, newMentions: string[]) => {
    setContent(newContent);
    setMentions(newMentions);
    
    // Clear content error when user starts typing
    if (localErrors.content) {
      setLocalErrors(prev => ({ ...prev, content: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: NoteFormErrors = {};

    if (!content.trim()) {
      newErrors.content = 'Note content is required';
    } else if (!NoteValidation.isValidContent(content)) {
      if (content.length > 2000) {
        newErrors.content = 'Note content must be 2000 characters or less';
      } else {
        newErrors.content = 'Note content is invalid';
      }
    }

    setLocalErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const formData: CreateNoteData | UpdateNoteData = {
      content: content.trim(),
      mentions: showMentions ? mentions : undefined,
    };

    if (!isEditing && transactionId) {
      (formData as CreateNoteData).transactionId = transactionId;
    }

    try {
      await onSubmit(formData);
      
      // Reset form if creating new note
      if (!isEditing) {
        setContent('');
        setMentions([]);
      }
    } catch (error) {
      console.error('Error submitting note form:', error);
    }
  };

  const handleCancel = () => {
    if (!isEditing) {
      setContent('');
      setMentions([]);
    }
    onCancel();
  };

  const getMentionedUsers = () => {
    return availableUsers.filter(user => mentions.includes(user.id));
  };

  const mentionedUsers = getMentionedUsers();

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* General Error */}
      {localErrors.general && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-600">{localErrors.general}</p>
        </div>
      )}

      {/* Content Input */}
      <div>
        <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
          {isEditing ? 'Edit Note' : 'Add Note'}
        </label>
        
        {showMentions ? (
          <MentionInput
            value={content}
            onChange={handleContentChange}
            placeholder={placeholder}
            disabled={loading}
            error={localErrors.content}
            availableUsers={availableUsers}
            maxLength={2000}
          />
        ) : (
          <div>
            <textarea
              id="content"
              value={content}
              onChange={(e) => handleContentChange(e.target.value, [])}
              placeholder={placeholder}
              disabled={loading}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 resize-none"
              style={{ minHeight: '100px', maxHeight: '200px' }}
            />
            {localErrors.content && (
              <p className="mt-1 text-sm text-red-600">{localErrors.content}</p>
            )}
            <div className="flex justify-between items-center mt-1">
              <div className="text-xs text-gray-500">
                {content.length}/2000 characters
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Mentioned Users Preview */}
      {showMentions && mentionedUsers.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <div className="text-sm font-medium text-blue-900 mb-2">
            Mentioning {mentionedUsers.length} user{mentionedUsers.length > 1 ? 's' : ''}:
          </div>
          <div className="flex flex-wrap gap-2">
            {mentionedUsers.map(user => (
              <div
                key={user.id}
                className="inline-flex items-center space-x-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"
              >
                <span>@{user.firstName} {user.lastName}</span>
              </div>
            ))}
          </div>
          <p className="text-xs text-blue-700 mt-2">
            These users will be notified about this note.
          </p>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={loading}
          disabled={loading || !content.trim()}
        >
          {isEditing ? 'Update Note' : 'Add Note'}
        </Button>
      </div>
    </form>
  );
}

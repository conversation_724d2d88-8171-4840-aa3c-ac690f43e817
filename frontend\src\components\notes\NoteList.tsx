/**
 * NoteList component
 *
 * Displays a list of notes with search, filtering, and pagination
 */

'use client';

import { useState, useEffect } from 'react';
import { NoteListProps, NoteSummary, NoteSearchCriteria, NoteValidation } from '@/types/note';
import { NoteCard } from './NoteCard';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useNotes, useDeleteNote } from '@/hooks';

// Icons
const SearchIcon = () => <span>🔍</span>;
const FilterIcon = () => <span>🔽</span>;
const ThreadIcon = () => <span>💬</span>;

// Remove the local state interface since we're using React Query

export function NoteList({
  transactionId,
  searchCriteria = {},
  onNoteSelect,
  onNoteEdit,
  onNoteDelete,
  showActions = true,
  compact = false,
  showThread = false,
}: NoteListProps) {
  const [criteria, setCriteria] = useState<NoteSearchCriteria>({
    ...searchCriteria,
    transactionId,
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Use React Query hooks
  const { data: notesData, isLoading, error, refetch } = useNotes(criteria);
  const deleteNoteMutation = useDeleteNote();

  // Extract data from React Query response
  const notes = notesData?.notes || [];
  const total = notesData?.total || 0;
  const totalPages = notesData?.totalPages || 1;

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCriteria(prev => ({
      ...prev,
      search: term || undefined,
      page: 1,
    }));
  };

  const handleCriteriaChange = (newCriteria: NoteSearchCriteria) => {
    setCriteria(prev => ({
      ...prev,
      ...newCriteria,
      page: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setCriteria(prev => ({ ...prev, page }));
  };

  const handleNoteSelect = (note: NoteSummary) => {
    if (onNoteSelect) {
      onNoteSelect(note as any); // Type conversion for compatibility
    }
  };

  const handleNoteEdit = (note: NoteSummary) => {
    if (onNoteEdit) {
      onNoteEdit(note as any); // Type conversion for compatibility
    }
  };

  const handleNoteDelete = async (noteId: string) => {
    try {
      await deleteNoteMutation.mutateAsync(noteId);
      if (onNoteDelete) {
        onNoteDelete(noteId);
      }
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Error deleting note:', error);
    }
  };

  const handleNoteReply = (note: NoteSummary) => {
    // TODO: Implement reply functionality
    console.log('Reply to note:', note.id);
  };

  const resetFilters = () => {
    setSearchTerm('');
    setCriteria({
      transactionId,
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
    setShowFilters(false);
  };

  if (isLoading && notes.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">Loading notes...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600">{error instanceof Error ? error.message : 'Failed to load notes'}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          className="mt-2"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon />
            </div>
            <Input
              type="text"
              placeholder="Search notes..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex gap-2">
          {showThread && (
            <Button
              variant="outline"
              leftIcon={<ThreadIcon />}
            >
              Thread View
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            leftIcon={<FilterIcon />}
          >
            Filters
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-sm text-gray-600">Note filters will be implemented here</p>
          {/* TODO: Implement note filters */}
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          {total === 0 ? 'No notes found' : `${total} note${total === 1 ? '' : 's'} found`}
        </p>

        {isLoading && (
          <LoadingSpinner size="sm" />
        )}
      </div>

      {/* Notes List */}
      {notes.length > 0 ? (
        <div className="space-y-4">
          {notes.map((note) => (
            <NoteCard
              key={note.id}
              note={note}
              onEdit={showActions ? handleNoteEdit : undefined}
              onDelete={showActions ? handleNoteDelete : undefined}
              onView={handleNoteSelect}
              onReply={showActions ? handleNoteReply : undefined}
              showActions={showActions}
              compact={compact}
              currentUserId="user-1" // TODO: Get from auth context
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No notes found</p>
          {transactionId && (
            <p className="text-sm text-gray-400">
              Add notes to start collaborating on this transaction
            </p>
          )}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(criteria.page! - 1)}
            disabled={criteria.page === 1 || isLoading}
          >
            Previous
          </Button>

          <span className="text-sm text-gray-600">
            Page {criteria.page} of {totalPages}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(criteria.page! + 1)}
            disabled={criteria.page === totalPages || isLoading}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}

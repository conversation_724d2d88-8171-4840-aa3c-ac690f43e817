/**
 * ContactManagement component
 * 
 * Main contact management interface that combines all contact components
 */

'use client';

import { useState } from 'react';
import { Contact, ContactSummary, ContactSearchCriteria } from '@/types/contact';
import { ContactList } from './ContactList';
import { ContactModal } from './ContactModal';
import { Button } from '@/components/ui/Button';

interface ContactManagementProps {
  transactionId?: string;
  title?: string;
  showAddButton?: boolean;
  compact?: boolean;
}

export function ContactManagement({
  transactionId,
  title = 'Contacts',
  showAddButton = true,
  compact = false,
}: ContactManagementProps) {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchCriteria, setSearchCriteria] = useState<ContactSearchCriteria>({
    transactionId,
  });

  const handleAddContact = () => {
    setSelectedContact(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleEditContact = (contact: ContactSummary) => {
    setSelectedContact(contact as Contact);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleViewContact = (contact: ContactSummary) => {
    setSelectedContact(contact as Contact);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleDeleteContact = async (contactId: string) => {
    try {
      // TODO: Replace with actual API call
      // await contactApi.deleteContact(contactId);
      console.log('Deleting contact:', contactId);
      
      // Mock successful deletion
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // The ContactList component will handle reloading
      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedContact(null);
  };

  const handleContactSave = (contact: Contact) => {
    // The ContactList component will handle reloading
    console.log('Contact saved:', contact);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        
        {showAddButton && (
          <Button
            onClick={handleAddContact}
            leftIcon={<span>➕</span>}
          >
            Add Contact
          </Button>
        )}
      </div>

      {/* Contact List */}
      <ContactList
        transactionId={transactionId}
        searchCriteria={searchCriteria}
        onContactSelect={handleViewContact}
        onContactEdit={handleEditContact}
        onContactDelete={handleDeleteContact}
        showActions={true}
        compact={compact}
      />

      {/* Contact Modal */}
      <ContactModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        contact={selectedContact}
        transactionId={transactionId}
        mode={modalMode}
        onSave={handleContactSave}
      />
    </div>
  );
}

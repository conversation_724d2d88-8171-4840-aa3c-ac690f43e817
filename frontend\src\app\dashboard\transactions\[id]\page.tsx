/**
 * Transaction Detail Page
 * 
 * Displays comprehensive transaction information including contacts and communication
 */

'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { ContactManagement } from '@/components/contacts';
import { NoteManagement } from '@/components/notes';
import { TransactionHeader } from '@/components/transactions/TransactionHeader';
import { TransactionTabs } from '@/components/transactions/TransactionTabs';
import { TransactionOverview } from '@/components/transactions/TransactionOverview';
import { TransactionTasks } from '@/components/transactions/TransactionTasks';
import { TransactionDocuments } from '@/components/transactions/TransactionDocuments';

type TabType = 'overview' | 'contacts' | 'communication' | 'tasks' | 'documents';

export default function TransactionDetailPage() {
  const params = useParams();
  const transactionId = params.id as string;
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  // Mock transaction data - in real app, this would come from API
  const mockTransaction = {
    id: transactionId,
    propertyAddress: '123 Main Street, Philadelphia, PA 19103',
    transactionType: 'PURCHASE' as const,
    status: 'ACTIVE' as const,
    contractDate: new Date('2024-01-01'),
    closingDate: new Date('2024-02-15'),
    salePrice: 450000,
    buyerName: 'John & Jane Smith',
    sellerName: 'Robert Johnson',
    createdAt: new Date('2023-12-15'),
    updatedAt: new Date('2024-01-10'),
    brokerage: {
      id: 'brokerage-1',
      name: 'Premier Realty Group',
    },
    tc: {
      id: 'user-1',
      firstName: 'Sarah',
      lastName: 'Wilson',
      email: '<EMAIL>',
    },
    listingAgent: {
      id: 'user-2',
      firstName: 'Mike',
      lastName: 'Chen',
      email: '<EMAIL>',
    },
    sellingAgent: {
      id: 'user-3',
      firstName: 'Lisa',
      lastName: 'Rodriguez',
      email: '<EMAIL>',
    },
    taskCount: 15,
    completedTaskCount: 8,
    documentCount: 12,
    contactCount: 6,
    noteCount: 23,
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'contacts', name: 'Contacts', icon: '👥', count: mockTransaction.contactCount },
    { id: 'communication', name: 'Communication', icon: '💬', count: mockTransaction.noteCount },
    { id: 'tasks', name: 'Tasks', icon: '✅', count: mockTransaction.taskCount },
    { id: 'documents', name: 'Documents', icon: '📄', count: mockTransaction.documentCount },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <TransactionOverview transaction={mockTransaction} />;
      
      case 'contacts':
        return (
          <ContactManagement
            transactionId={transactionId}
            title="Transaction Contacts"
            showAddButton={true}
            compact={false}
          />
        );
      
      case 'communication':
        return (
          <NoteManagement
            transactionId={transactionId}
            title="Team Communication"
            showAddButton={true}
            compact={false}
            defaultView="list"
            currentUserId="user-1" // TODO: Get from auth context
          />
        );
      
      case 'tasks':
        return <TransactionTasks transactionId={transactionId} />;
      
      case 'documents':
        return <TransactionDocuments transactionId={transactionId} />;
      
      default:
        return <TransactionOverview transaction={mockTransaction} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Transaction Header */}
      <TransactionHeader transaction={mockTransaction} />

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <TransactionTabs
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>
    </div>
  );
}

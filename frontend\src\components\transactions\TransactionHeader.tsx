/**
 * TransactionHeader component
 * 
 * Displays transaction header with key information and actions
 */

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface TransactionHeaderProps {
  transaction: {
    id: string;
    propertyAddress: string;
    transactionType: 'PURCHASE' | 'SALE' | 'LEASE';
    status: 'PENDING' | 'ACTIVE' | 'UNDER_CONTRACT' | 'CLOSED' | 'CANCELLED';
    contractDate?: Date;
    closingDate?: Date;
    salePrice?: number;
    buyerName?: string;
    sellerName?: string;
    tc?: {
      id: string;
      firstName: string;
      lastName: string;
    };
    listingAgent?: {
      id: string;
      firstName: string;
      lastName: string;
    };
    sellingAgent?: {
      id: string;
      firstName: string;
      lastName: string;
    };
    taskCount: number;
    completedTaskCount: number;
  };
}

export function TransactionHeader({ transaction }: TransactionHeaderProps) {
  const [showActions, setShowActions] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'yellow';
      case 'ACTIVE':
        return 'blue';
      case 'UNDER_CONTRACT':
        return 'purple';
      case 'CLOSED':
        return 'green';
      case 'CANCELLED':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getTypeDisplay = (type: string) => {
    switch (type) {
      case 'PURCHASE':
        return 'Purchase';
      case 'SALE':
        return 'Sale';
      case 'LEASE':
        return 'Lease';
      default:
        return type;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };

  const progressPercentage = Math.round((transaction.completedTaskCount / transaction.taskCount) * 100);

  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex items-start justify-between">
          {/* Left side - Transaction info */}
          <div className="flex-1 min-w-0">
            {/* Property address and status */}
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900 truncate">
                {transaction.propertyAddress}
              </h1>
              <Badge color={getStatusColor(transaction.status)}>
                {transaction.status.replace('_', ' ')}
              </Badge>
            </div>

            {/* Transaction details */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <span className="font-medium">Type:</span>
                <span>{getTypeDisplay(transaction.transactionType)}</span>
              </div>

              {transaction.salePrice && (
                <div className="flex items-center space-x-1">
                  <span className="font-medium">Price:</span>
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(transaction.salePrice)}
                  </span>
                </div>
              )}

              {transaction.contractDate && (
                <div className="flex items-center space-x-1">
                  <span className="font-medium">Contract:</span>
                  <span>{formatDate(transaction.contractDate)}</span>
                </div>
              )}

              {transaction.closingDate && (
                <div className="flex items-center space-x-1">
                  <span className="font-medium">Closing:</span>
                  <span>{formatDate(transaction.closingDate)}</span>
                </div>
              )}
            </div>

            {/* Parties */}
            <div className="flex flex-wrap items-center gap-6 mt-3 text-sm text-gray-600">
              {transaction.buyerName && (
                <div className="flex items-center space-x-1">
                  <span className="font-medium">Buyer:</span>
                  <span>{transaction.buyerName}</span>
                </div>
              )}

              {transaction.sellerName && (
                <div className="flex items-center space-x-1">
                  <span className="font-medium">Seller:</span>
                  <span>{transaction.sellerName}</span>
                </div>
              )}
            </div>

            {/* Team members */}
            <div className="flex flex-wrap items-center gap-6 mt-3 text-sm text-gray-600">
              {transaction.tc && (
                <div className="flex items-center space-x-1">
                  <span className="font-medium">TC:</span>
                  <span>{transaction.tc.firstName} {transaction.tc.lastName}</span>
                </div>
              )}

              {transaction.listingAgent && (
                <div className="flex items-center space-x-1">
                  <span className="font-medium">Listing Agent:</span>
                  <span>{transaction.listingAgent.firstName} {transaction.listingAgent.lastName}</span>
                </div>
              )}

              {transaction.sellingAgent && (
                <div className="flex items-center space-x-1">
                  <span className="font-medium">Selling Agent:</span>
                  <span>{transaction.sellingAgent.firstName} {transaction.sellingAgent.lastName}</span>
                </div>
              )}
            </div>
          </div>

          {/* Right side - Progress and actions */}
          <div className="flex items-start space-x-4 ml-6">
            {/* Progress indicator */}
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {progressPercentage}%
              </div>
              <div className="text-xs text-gray-500">
                {transaction.completedTaskCount} of {transaction.taskCount} tasks
              </div>
              <div className="w-16 bg-gray-200 rounded-full h-2 mt-1">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowActions(!showActions)}
              >
                Actions
              </Button>
              <Button size="sm">
                Edit Transaction
              </Button>
            </div>
          </div>
        </div>

        {/* Action menu */}
        {showActions && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" size="sm">
                📧 Send Update
              </Button>
              <Button variant="outline" size="sm">
                📄 Generate Report
              </Button>
              <Button variant="outline" size="sm">
                📅 Schedule Meeting
              </Button>
              <Button variant="outline" size="sm">
                🔄 Change Status
              </Button>
              <Button variant="outline" size="sm">
                📋 Clone Transaction
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
